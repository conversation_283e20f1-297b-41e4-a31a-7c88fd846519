// --- START OF FILE gameLogic.js ---

let lastResetSection, resetActive,
    resetEndTime, lastFragmentCollisionTime;

// Helper function to get the tunnel mesh
function getTunnelMesh() {
    return tunnelMesh;
}

// Helper function to get lasers
function getLasers() {
    return lasers || [];
}

// Helper function to call createBunny
async function callCreateBunny(scene) {
    if (typeof createBunny === 'function') {
        return createBunny(scene);
    } else {
        console.error("createBunny function is not defined!");
        throw new Error("Bunny creation function not available");
    }
}

// Helper function to set the game level
function setGameLevel(level) {
    if (level >= 1 && level <= 18) {
        gameLevel = level;
        if (DEBUG_MODE) console.log(`Game level set to ${gameLevel}`);
    } else {
        console.warn(`Invalid game level: ${level}. Must be between 1 and 18.`);
    }
}

// --- BALANS ROZGRYWKI (przeniesione z gameBalanceAdjustments.js) ---

const BALANCED_CONFIG = {
    PLAQUE_DAMAGE_MULTIPLIER: 0.5,
    ENERGY_MODIFIER_EASY: 0.5,
    ENERGY_MODIFIER_MEDIUM: 0.7,
    ENERGY_MODIFIER_HARD: 1.0,
    AMMO_REGEN_INTERVAL: 1500,
    ENERGY_PER_EGG_BONUS: 5,
    COLLISION_DAMAGE_REDUCTION: 0.6,
};

function checkPlaqueCollisionsBalanced(playerMesh) {
    if (!playerMesh || !Array.isArray(atheroscleroticPlaques)) return 0;
    const now = Date.now();
    const cooldown = 500;
    if (now - lastPlaqueCollisionTime < cooldown) {
        return 0;
    }
    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (!plaque.mesh || plaque.mesh.isDisposed()) continue;
        let collision = false;
        if (plaque.mesh.getChildren && plaque.mesh.getChildren().length > 0) {
            const children = plaque.mesh.getChildren();
            for (let k = 0; k < children.length; k++) {
                const child = children[k];
                if (child.getBoundingInfo && playerMesh.intersectsMesh(child, true)) {
                    collision = true;
                    break;
                }
            }
        } else if (plaque.mesh.getBoundingInfo) {
            collision = playerMesh.intersectsMesh(plaque.mesh, true);
        }
        if (collision) {
            lastPlaqueCollisionTime = now;
            if (!encounteredPlaqueTypes.has(plaque.ahaType)) {
                encounteredPlaqueTypes.add(plaque.ahaType);
                showObjectTypeInfo('plaque', plaque);
            }
            let damage = (plaque.damage || 10) * BALANCED_CONFIG.PLAQUE_DAMAGE_MULTIPLIER;
            const difficultyModifier = getBalancedDifficultyModifier();
            damage *= difficultyModifier;
            if (typeof playParticleEffect === 'function') {
                playParticleEffect("obstacleCollision", playerMesh.position);
            }
            console.log(`Kolizja ze zmianą miażdżycową typu AHA ${plaque.ahaType}. Obrażenia: ${damage.toFixed(1)}`);
            return damage;
        }
    }
    return 0;
}

function getBalancedDifficultyModifier() {
    const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];
    const diameter = currentSegment.diameter;
    if (diameter >= 3.5) {
        return BALANCED_CONFIG.ENERGY_MODIFIER_EASY;
    } else if (diameter >= 2.5) {
        return BALANCED_CONFIG.ENERGY_MODIFIER_MEDIUM;
    } else {
        return BALANCED_CONFIG.ENERGY_MODIFIER_HARD;
    }
}

// --- Główna Logika Gry, Pętla, Stan ---

/**
 * Ulepszona funkcja gameLoop z dodatkowymi sprawdzeniami i ograniczeniami
 * Zintegrowana z gameFixesIntegration.js
 */
function gameLoop() {
    // KRYTYCZNE: Sprawdź czy gra powinna działać
    const currentScene = scene || window.scene;
    const currentEngine = engine || window.engine;
    if (!gameRunning || !currentScene || !currentEngine) return;
    if (window.gameState && window.gameState.paused) return;

    // Monitoring co 5 sekund
    if (window.frameCounter % 300 === 0) {
        console.log("=== PERFORMANCE CHECK ===");
        console.log("Scene meshes:", currentScene.meshes.length);
        console.log("Active objects:", {
            whiteBloodCells: whiteBloodCells ? whiteBloodCells.length : 0,
            yellowLeukocytes: yellowLeukocytes ? yellowLeukocytes.length : 0,
            eggs: eggs ? eggs.length : 0,
            obstacles: obstacles ? obstacles.length : 0
        });
    }

    // Dodaj licznik klatek dla optymalizacji
    window.frameCounter = (window.frameCounter || 0) + 1;

    const deltaTime = currentEngine.getDeltaTime() / 1000.0;
    const cappedDeltaTime = Math.min(deltaTime, 0.05);

    // Player movement is handled later in the gameLoop when cameraReachedPlayer is true

    // Wykryj niską liczbę klatek na sekundę i dostosuj aktualizacje
    const lowFPS = deltaTime > 0.033; // Mniej niż 30 FPS

    // Create or update waiting message function
    const showWaitingMessage = (message) => {
        let waitingMessage = document.getElementById('waitingMessage');
        if (!waitingMessage) {
            waitingMessage = document.createElement('div');
            waitingMessage.id = 'waitingMessage';
            waitingMessage.style.position = 'absolute';
            waitingMessage.style.top = '50%';
            waitingMessage.style.left = '50%';
            waitingMessage.style.transform = 'translate(-50%, -50%)';
            waitingMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            waitingMessage.style.color = 'white';
            waitingMessage.style.padding = '20px';
            waitingMessage.style.borderRadius = '10px';
            waitingMessage.style.fontSize = '24px';
            waitingMessage.style.textAlign = 'center';
            waitingMessage.style.zIndex = '100';
            document.body.appendChild(waitingMessage);
        }
        waitingMessage.textContent = message;
        return waitingMessage;
    };

    // Oryginalne sprawdzenia
    if (!currentScene.isReady()) {
        showWaitingMessage('Waiting for scene to fully initialize...');
        updateHUD();
        updateMinimap();
        return;
    }

    // Check if camera has reached player
    if (!cameraReachedPlayer && camera && bunnyCollider) {
        const cameraPosition = camera.position;
        const playerPosition = bunnyCollider.position;
        const distance = BABYLON.Vector3.Distance(cameraPosition, playerPosition);
        const threshold = Config.CAMERA_RADIUS * 1.2;

        if (distance <= threshold) {
            cameraReachedPlayer = true;

            // Ustaw ostateczne parametry kamery po dotarciu do gracza
            if (camera.cameraAcceleration !== undefined) {
                camera.cameraAcceleration = Config.CAMERA_ACCELERATION; // Przywróć normalne przyspieszenie
                camera.maxCameraSpeed = Config.CAMERA_MAX_SPEED; // Przywróć normalną prędkość
            }

            // Wywołaj resetCameraPosition, aby zapewnić spójne ustawienie kamery
            // zgodnie z aktualnym trybem kamery (FPP lub TPP)
            if (typeof resetCameraPosition === 'function') {
                resetCameraPosition();
            }

            // Remove waiting message if it exists
            const waitingMessage = document.getElementById('waitingMessage');
            if (waitingMessage) {
                waitingMessage.remove();
            }
            if (DEBUG_MODE) console.log("Camera has reached player. Starting gameplay!");
        } else {
            // Show waiting message for camera
            showWaitingMessage('Camera approaches player...');
            // Only update HUD and minimap while waiting for camera
            updateHUD();
            updateMinimap();
            return; // Skip game updates until camera reaches player
        }
    } else if (cameraReachedPlayer) {
        // Upewnij się, że komunikat oczekiwania jest usunięty, gdy kamera już dotarła do gracza
        const waitingMessage = document.getElementById('waitingMessage');
        if (waitingMessage) {
            waitingMessage.remove();
        }
    }

    // Przetwarzaj kolejkę obiektów niezależnie od stanu kamery
    if (typeof processObjectQueue === 'function') {
        processObjectQueue();
    }

    // Only run game logic if camera has reached player
    if (cameraReachedPlayer) {
        updateGameSpeed(cappedDeltaTime);
        updateBunnyPosition(cappedDeltaTime);
        updateLasers(cappedDeltaTime);
        updateEggs(cappedDeltaTime);

        // ===== ZMIENIONE: Użyj zoptymalizowanego systemu biologicznego =====
        if (typeof updateBiologicalManager === 'function') {
            updateBiologicalManager(cappedDeltaTime);
        } else {
            // Fallback do starych funkcji jeśli nowy system nie jest dostępny
            if (typeof updateOptimizedBloodCells === 'function') {
                updateOptimizedBloodCells(cappedDeltaTime);
            } else if (typeof updateRedBloodCells === 'function') {
                updateRedBloodCells(cappedDeltaTime);
            }

            // Aktualizuj białe krwinki tylko jeśli istnieją
            if (typeof updateWhiteBloodCells === 'function' &&
                Array.isArray(whiteBloodCells) && whiteBloodCells.length > 0) {
                updateWhiteBloodCells(cappedDeltaTime);
            }

            // Aktualizuj żółte leukocyty tylko jeśli istnieją
            if (typeof updateYellowLeukocytes === 'function' &&
                Array.isArray(yellowLeukocytes) && yellowLeukocytes.length > 0) {
                updateYellowLeukocytes(cappedDeltaTime);
            }
        }

        // === DODANE: Aktualizacja zmian miażdżycowych ===
        if (typeof updateAtheroscleroticPlaques === 'function') {
            updateAtheroscleroticPlaques(cappedDeltaTime);
        }

        // ===== DODANE: Sprawdź kolizje tylko dla aktywnych obiektów =====
        if (typeof checkOptimizedCollisions === 'function') {
            checkOptimizedCollisions();
        } else {
            // Sprawdź kolizje laserów z białymi krwinkami tylko jeśli istnieją
            if (typeof checkLaserWhiteBloodCellCollisions === 'function' &&
                Array.isArray(lasers) && lasers.length > 0 &&
                Array.isArray(whiteBloodCells) && whiteBloodCells.length > 0) {
                checkLaserWhiteBloodCellCollisions();
            }

            // Sprawdź kolizje laserów z żółtymi leukocytami tylko jeśli istnieją
            if (typeof checkLaserYellowLeukocyteCollisions === 'function' &&
                Array.isArray(lasers) && lasers.length > 0 &&
                Array.isArray(yellowLeukocytes) && yellowLeukocytes.length > 0) {
                checkLaserYellowLeukocyteCollisions();
            }
        }

        // Sprawdź kolizje laserów z blaszkami miażdżycowymi
        if (typeof checkLaserPlaqueCollisions === 'function' &&
            Array.isArray(lasers) && lasers.length > 0 &&
            Array.isArray(atheroscleroticPlaques) && atheroscleroticPlaques.length > 0) {
            checkLaserPlaqueCollisions();
        }

        // Sprawdź, czy należy oderwać zmiany miażdżycowe podczas spowolnienia
        if (typeof detachWallCubesInSlowState === 'function') {
            detachWallCubesInSlowState();
        }

        checkCollisions();
        updateHUD();
        updateMinimap();
        removeOldObjects();

        // Camera updates are now handled by updateCameraWithConstraints in world.js
        // This prevents conflicts between multiple camera update systems
        if (typeof updateCameraWithConstraints === 'function') {
            updateCameraWithConstraints();
        }
    }

    /**
     * Ulepszona funkcja usuwania starych obiektów z dodatkowymi sprawdzeniami
     * Zoptymalizowana dla lepszej wydajności
     */
    function removeOldObjects() {
        if (!bunnyCollider || !scene || !gameRunning) return;

        const playerZ = bunnyCollider.position.z;
        const removalDistance = 30; // Zwiększona odległość usuwania dla mniejszej częstotliwości sprawdzeń

        // Limit liczby obiektów do przetworzenia w jednej klatce
        const maxObjectsPerType = 20; // Maksymalna liczba obiektów jednego typu do sprawdzenia

        // Usuń jajka poza tunelem lub za graczem
        if (eggs && eggs.length > 0) {
            // Przetwarzaj tylko część tablicy w każdym wywołaniu
            const startIdx = Math.max(0, eggs.length - maxObjectsPerType);
            const endIdx = eggs.length - 1;

            for (let i = endIdx; i >= startIdx; i--) {
                if (i >= eggs.length) continue; // Zabezpieczenie przed zmianą rozmiaru tablicy

                const egg = eggs[i];
                if (!egg?.mesh || egg.mesh.isDisposed() || !egg.mesh.isEnabled()) {
                    if (egg && (!egg.mesh || egg.mesh.isDisposed())) {
                        egg.light?.dispose();
                        eggs.splice(i, 1);
                    }
                    continue;
                }

                // Szybkie sprawdzenie odległości przed bardziej kosztownym sprawdzeniem tunelu
                if (egg.mesh.position.z < playerZ - removalDistance) {
                    if (DEBUG_MODE) console.log(`Removing egg at position ${egg.mesh.position.z} (player at ${playerZ})`);
                    egg.light?.dispose();
                    egg.mesh.dispose();
                    eggs.splice(i, 1);
                    continue;
                }

                // Sprawdź czy jajko jest w tunelu tylko jeśli jest blisko gracza
                if (Math.abs(egg.mesh.position.z - playerZ) < removalDistance && !isPositionInTunnel(egg.mesh.position)) {
                    if (DEBUG_MODE) console.log(`Removing egg outside tunnel at position ${egg.mesh.position.z}`);
                    egg.light?.dispose();
                    egg.mesh.dispose();
                    eggs.splice(i, 1);
                }
            }
        }

        // Usuń przeszkody poza tunelem lub za graczem
        if (obstacles && obstacles.length > 0) {
            // Przetwarzaj tylko część tablicy w każdym wywołaniu
            const startIdx = Math.max(0, obstacles.length - maxObjectsPerType);
            const endIdx = obstacles.length - 1;

            for (let i = endIdx; i >= startIdx; i--) {
                if (i >= obstacles.length) continue; // Zabezpieczenie przed zmianą rozmiaru tablicy

                const obstacle = obstacles[i];
                if (!obstacle?.mesh || obstacle.mesh.isDisposed() || !obstacle.mesh.isEnabled()) {
                    if (obstacle && (!obstacle.mesh || obstacle.mesh.isDisposed())) {
                        obstacle.light?.dispose();
                        obstacle.mesh?.physicsImpostor?.dispose();
                        obstacles.splice(i, 1);
                    }
                    continue;
                }

                // Szybkie sprawdzenie odległości przed bardziej kosztownym sprawdzeniem tunelu
                if (obstacle.mesh.position.z < playerZ - removalDistance) {
                    if (DEBUG_MODE) console.log(`Removing obstacle at position ${obstacle.mesh.position.z} (player at ${playerZ})`);
                    obstacle.light?.dispose();
                    obstacle.mesh.physicsImpostor?.dispose();
                    obstacle.mesh.dispose();
                    obstacles.splice(i, 1);
                    continue;
                }

                // Sprawdź czy przeszkoda jest w tunelu tylko jeśli jest blisko gracza
                if (Math.abs(obstacle.mesh.position.z - playerZ) < removalDistance && !isPositionInTunnel(obstacle.mesh.position)) {
                    if (DEBUG_MODE) console.log(`Removing obstacle outside tunnel at position ${obstacle.mesh.position.z}`);
                    obstacle.light?.dispose();
                    obstacle.mesh.physicsImpostor?.dispose();
                    obstacle.mesh.dispose();
                    obstacles.splice(i, 1);
                }
            }
        }

        // Usuń kostki ścienne poza tunelem lub za graczem
        if (wallCubes && wallCubes.length > 0) {
            // Przetwarzaj tylko część tablicy w każdym wywołaniu
            const startIdx = Math.max(0, wallCubes.length - maxObjectsPerType);
            const endIdx = wallCubes.length - 1;

            for (let i = endIdx; i >= startIdx; i--) {
                if (i >= wallCubes.length) continue; // Zabezpieczenie przed zmianą rozmiaru tablicy

                const cubeData = wallCubes[i];
                if (!cubeData?.mesh || cubeData.mesh.isDisposed() || !cubeData.mesh.isEnabled()) {
                    if (cubeData && (!cubeData.mesh || cubeData.mesh.isDisposed())) {
                        cubeData.mesh?.physicsImpostor?.dispose();
                        wallCubes.splice(i, 1);
                    }
                    continue;
                }

                // Szybkie sprawdzenie odległości przed bardziej kosztownym sprawdzeniem tunelu
                if (cubeData.mesh.position.z < playerZ - removalDistance) {
                    if (DEBUG_MODE) console.log(`Removing wall cube at position ${cubeData.mesh.position.z} (player at ${playerZ})`);
                    cubeData.mesh.physicsImpostor?.dispose();
                    cubeData.mesh.dispose();
                    wallCubes.splice(i, 1);
                    continue;
                }

                // Sprawdź czy kostka jest w tunelu tylko jeśli jest blisko gracza
                if (Math.abs(cubeData.mesh.position.z - playerZ) < removalDistance && !isPositionInTunnel(cubeData.mesh.position)) {
                    if (DEBUG_MODE) console.log(`Removing wall cube outside tunnel at position ${cubeData.mesh.position.z}`);
                    cubeData.mesh.physicsImpostor?.dispose();
                    cubeData.mesh.dispose();
                    wallCubes.splice(i, 1);
                }
            }
        }
    }

    /**
     * Sprawdza integralność gry i poprawia błędy
     */
    function checkGameIntegrity() {
        const currentScene = scene || window.scene;
        if (!gameRunning || !currentScene) return;

        let fixCount = 0;

        // Sprawdź kamerę
        if (camera && bunnyCollider && !isPositionInTunnel(camera.position)) {
            constrainCameraToTunnel(camera, bunnyCollider.position);
            fixCount++;
        }

        // Sprawdź gracza
        if (bunnyCollider && !isPositionInTunnel(bunnyCollider.position)) {
            bunnyCollider.position = constrainPositionToTunnel(bunnyCollider.position, 0.9);
            fixCount++;
        }

        // Sprawdź obiekty poza tunelem
        const allObjects = [...(eggs || []), ...(obstacles || []), ...(wallCubes || [])];
        allObjects.forEach(obj => {
            if (obj?.mesh && !obj.mesh.isDisposed() && !isPositionInTunnel(obj.mesh.position)) {
                obj.mesh.position = constrainPositionToTunnel(obj.mesh.position, 0.85);
                fixCount++;
            }
        });

        if (fixCount > 0 && DEBUG_MODE) {
            console.log(`Game integrity check: Fixed ${fixCount} objects outside tunnel`);
        }
    }

    // Uruchom sprawdzanie integralności gry co 5 sekund
    setInterval(checkGameIntegrity, 5000);
}

// Znajdowanie sekcji tunelu
function findSectionAtPosition(positionZ) {
    // Optimize slightly by starting search near the last known index
    const searchStartIndex = Math.max(0, currentSectionIndex - 5); // Check a bit behind
    const searchEndIndex = Math.min(tunnelSections.length, currentSectionIndex + 5); // Check a bit ahead

    // First check around the current index
    for (let i = searchStartIndex; i < searchEndIndex; i++) {
        const section = tunnelSections[i];
        const minZ = Math.min(section.startPoint.z, section.endPoint.z);
        const maxZ = Math.max(section.startPoint.z, section.endPoint.z);
        // Use a small epsilon for floating point comparisons
        const epsilon = 0.01;
        if (positionZ >= minZ - epsilon && positionZ < maxZ + epsilon) {
            currentSectionIndex = i; // Update current index using setter
            return section;
        }
    }

    // If not found nearby, search the entire tunnel (less efficient, fallback)
    for (let i = 0; i < tunnelSections.length; i++) {
        const section = tunnelSections[i];
        const minZ = Math.min(section.startPoint.z, section.endPoint.z);
        const maxZ = Math.max(section.startPoint.z, section.endPoint.z);
        const epsilon = 0.01;
        if (positionZ >= minZ - epsilon && positionZ < maxZ + epsilon) {
            if (DEBUG_MODE) console.log(`findSectionAtPosition: Found section ${i} via full scan.`);
            currentSectionIndex = i; // Update current index using setter
            return section;
        }
    }

    // If still not found, determine if player is before start or after end
    if (tunnelSections.length > 0) {
        const firstSectionMinZ = Math.min(tunnelSections[0].startPoint.z, tunnelSections[0].endPoint.z);
        const lastSectionMaxZ = Math.max(tunnelSections[tunnelSections.length - 1].startPoint.z, tunnelSections[tunnelSections.length - 1].endPoint.z);

        if (positionZ < firstSectionMinZ) {
            if (DEBUG_MODE) console.warn(`Position Z ${positionZ.toFixed(2)} is before the tunnel start.`);
            currentSectionIndex = 0; // Consider player in the first section logically
            return tunnelSections[0];
        } else if (positionZ >= lastSectionMaxZ) {
            if (DEBUG_MODE) console.warn(`Position Z ${positionZ.toFixed(2)} is after the tunnel end.`);
            currentSectionIndex = tunnelSections.length - 1; // Consider player in the last section
            return tunnelSections[tunnelSections.length - 1];
        }
    }

    if (DEBUG_MODE) console.warn(`Position Z ${positionZ.toFixed(2)} could not be mapped to any tunnel section.`);
    return null; // Truly outside or tunnel empty
}


// Update fleeing eggs
function updateEggs(deltaTime) {
    if (!bunnyCollider || !gameRunning || eggs.length === 0) return; // Early exit if no eggs

    const fleeRadiusSq = Config.EGG_FLEE_DETECTION_RADIUS * Config.EGG_FLEE_DETECTION_RADIUS;
    const fleeSpeed = Config.EGG_FLEE_SPEED * deltaTime * 100; // Base flee speed adjustment
    // Maximum distance an egg can flee from the player
    const maxFleeDistance = 10.0; // Limit how far eggs can flee

    for (let i = eggs.length - 1; i >= 0; i--) { // Iterate backwards for safe removal
        const egg = eggs[i];
        if (!egg.mesh || egg.mesh.isDisposed() || !egg.color) {
            if (DEBUG_MODE && egg && egg.mesh && !egg.mesh.isDisposed()) console.warn(`updateEggs: Skipping egg ${i} - mesh exists but is disposed or color missing.`);
            // Ensure removal if mesh somehow got disposed externally
            if (!egg.mesh || egg.mesh.isDisposed()) {
                egg.light?.dispose();
                eggs.splice(i, 1);
            }
            continue;
        }

        // Fleeing logic
        if (Config.FLEEING_EGG_COLORS.includes(egg.color)) {
            const distanceSq = BABYLON.Vector3.DistanceSquared(bunnyCollider.position, egg.mesh.position);
            const distance = Math.sqrt(distanceSq);

            if (distanceSq < fleeRadiusSq) {
                // If egg is already at max flee distance, don't let it flee further
                if (distance >= maxFleeDistance) {
                    // Instead, make it move perpendicular to the player direction
                    // to make it still catchable but not move further away
                    const playerToEgg = egg.mesh.position.subtract(bunnyCollider.position).normalize();
                    const perpendicularDir = new BABYLON.Vector3(
                        -playerToEgg.y,
                        playerToEgg.x,
                        0
                    ).normalize();

                    // Add some randomness to the perpendicular movement
                    const randomFactor = Math.random() * 2 - 1; // -1 to 1
                    perpendicularDir.scaleInPlace(randomFactor);

                    const displacement = perpendicularDir.scale(fleeSpeed * 0.5); // Slower perpendicular movement
                    egg.mesh.position.addInPlace(displacement);
                } else {
                    // Normal fleeing behavior when not at max distance
                    const fleeDirection = egg.mesh.position.subtract(bunnyCollider.position);
                    if (fleeDirection.lengthSquared() < 0.01) {
                        fleeDirection.set(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5);
                    }
                    fleeDirection.normalize();

                    const displacement = fleeDirection.scale(fleeSpeed);
                    egg.mesh.position.addInPlace(displacement); // Move the egg
                }

                // --- DEBUG LOG ---
                // if (DEBUG_MODE && i === 0) { // Log only one fleeing egg per frame for less spam
                //     console.log(`Egg ${i} (Color: ${egg.color}) fleeing. DistSq: ${distanceSq.toFixed(1)}, FleeDir: ${fleeDirection.toString()}, Disp: ${displacement.length().toFixed(3)}`);
                // }
                // --- END DEBUG LOG ---

                // Simple tunnel boundary check (optional but good)
                const eggSection = findSectionAtPosition(egg.mesh.position.z);
                if (eggSection) {
                    const centerOffsetVec = egg.mesh.position.subtract(eggSection.centerPoint);
                    const axisDir = eggSection.direction;
                    const projLen = BABYLON.Vector3.Dot(centerOffsetVec, axisDir);
                    const radialOffsetVec = centerOffsetVec.subtract(axisDir.scale(projLen));
                    const distFromAxis = radialOffsetVec.length();
                    const maxDist = eggSection.radius * 0.95; // 95% of radius

                    if (distFromAxis > maxDist) {
                        // Jeśli jajko jest zbyt daleko od ściany tunelu, zamiast tylko ograniczać pozycję,
                        // skieruj je z powrotem do środka tunelu
                        if (distFromAxis > maxDist * 1.2) {
                            // Jajko uciekło zbyt daleko - zacznij kierować je do środka
                            if (!egg.returnToCenter) {
                                egg.returnToCenter = true;
                                if (DEBUG_MODE) console.log(`Egg ${i} escaped from wall - returning to center`);

                                // Zmień kolor jajka na czerwony podczas powrotu do środka
                                const returnMaterial = new BABYLON.StandardMaterial("eggReturnMaterial", scene);
                                returnMaterial.diffuseColor = new BABYLON.Color3(1, 0.3, 0.3);
                                returnMaterial.emissiveColor = new BABYLON.Color3(0.5, 0.1, 0.1);
                                returnMaterial.specularColor = new BABYLON.Color3(1, 0.5, 0.5);
                                egg.originalMaterial = egg.mesh.material;
                                egg.mesh.material = returnMaterial;
                            }

                            // Oblicz kierunek do środka tunelu
                            const directionToCenter = eggSection.centerPoint.subtract(egg.mesh.position).normalize();

                            // Dodaj wektor prędkości w kierunku środka
                            const returnSpeed = 0.05 * deltaTime * 100;
                            egg.mesh.position.addInPlace(directionToCenter.scale(returnSpeed));

                            // Dodaj efekt obracania się podczas powrotu do środka
                            egg.mesh.rotation.x += deltaTime * 2;
                            egg.mesh.rotation.y += deltaTime * 2;
                            egg.mesh.rotation.z += deltaTime * 2;
                        } else {
                            // Standardowe ograniczenie pozycji
                            const correctedOffset = radialOffsetVec.normalize().scale(maxDist);
                            const clampedPosition = eggSection.centerPoint.add(axisDir.scale(projLen)).add(correctedOffset);
                            egg.mesh.position.copyFrom(clampedPosition);

                            // Jeśli jajko wróciło już do ściany, przywróć normalny wygląd
                            if (egg.returnToCenter) {
                                egg.returnToCenter = false;
                                if (egg.originalMaterial) {
                                    egg.mesh.material = egg.originalMaterial;
                                    egg.originalMaterial = null;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Check for missed eggs (moved behind the removal distance)
        // This is handled in removeOldObjects now, avoid duplication here.
    }
}


/**
 * Checks for collisions between the player (bunnyCollider) and various game objects
 * (eggs, obstacles, wall cubes). Applies effects, scoring, energy changes, and
 * triggers game over if necessary.
 */

/**
 * Destroys an obstacle, removing it from the game and adding points to the score.
 * @param {Object} obstacle - The obstacle object to destroy
 * @param {number} index - The index of the obstacle in the obstacles array
 * @param {number} pointsToAdd - Points to add to the score (0 for collision, more for laser destruction)
 */
function destroyObstacle(obstacle, index, pointsToAdd = 0) {
    if (!obstacle) return;

    // Play destruction effects
    if (obstacle.mesh && !obstacle.mesh.isDisposed()) {
        playParticleEffect("obstacleDestruction", obstacle.mesh.position);
        playSoundEffect(destructionSound);

        // Dispose of the obstacle's physics impostor if it exists
        if (obstacle.mesh.physicsImpostor) {
            obstacle.mesh.physicsImpostor.dispose();
        }

        // Dispose of the mesh
        obstacle.mesh.dispose();
    }

    // Dispose of the light if it exists
    if (obstacle.light) {
        obstacle.light.dispose();
    }

    // Remove the obstacle from the obstacles array
    if (index >= 0 && index < obstacles.length) {
        obstacles.splice(index, 1);
    }

    // Add points to the score if specified
    if (pointsToAdd > 0) {
        score += pointsToAdd;
        if (DEBUG_MODE) console.log(`Obstacle destroyed by laser! Score: ${score} (+${pointsToAdd})`);
    } else {
        if (DEBUG_MODE) console.log(`Obstacle destroyed by collision!`);
    }
}

function checkCollisions() {
    // Initial checks for essential objects and game state
    if (!bunnyCollider || !scene || !gameRunning || !tunnelSections || tunnelSections.length === 0) {
        return; // Cannot check collisions if core elements are missing or game not running
    }

    // Find player's current section (needed for context, though not directly used in collision checks here)
    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) {
        if (DEBUG_MODE) console.warn("checkCollisions: Player outside tunnel bounds, skipping checks.");
        return; // Skip checks if player somehow got outside defined sections
    }

    const now = Date.now(); // Get current time for cooldown checks

    // --- Collision Cooldown Intervals (in milliseconds) ---
    // Adjust these values based on gameplay feel, especially with higher speeds
    const minObstacleCollisionInterval = 400; // Min time between obstacle hits registering damage
    const minWallCubeCollisionInterval = 200; // Min time between wall cube hits registering damage
    const minPlaqueCollisionInterval = 300; // Min time between atherosclerotic plaque hits registering damage

    // Initialize cooldown timestamps if they don't exist
    if (typeof lastCollisionTime === 'undefined') lastCollisionTime = 0;
    if (typeof lastWallCubeCollisionTime === 'undefined') lastWallCubeCollisionTime = 0;
    if (typeof lastFragmentCollisionTime === 'undefined') lastFragmentCollisionTime = 0;
    if (typeof lastPlaqueCollisionTime === 'undefined') lastPlaqueCollisionTime = 0; // DODANE

    // === DODANE: Sprawdzenie kolizji ze zmianami miażdżycowymi ===
    if (typeof checkPlaqueCollisions === 'function' && now - lastPlaqueCollisionTime > minPlaqueCollisionInterval) {
        const plaqueDamage = checkPlaqueCollisions(bunnyCollider);
        if (plaqueDamage > 0) {
            lastPlaqueCollisionTime = now;

            // Zastosuj modyfikator trudności
            const difficultyModifier = getDifficultyModifier();
            const actualDamage = plaqueDamage * difficultyModifier;

            if (DEBUG_MODE) console.log(`Atherosclerotic plaque collision! Damage: ${actualDamage.toFixed(1)}`);

            energy = Math.max(0, energy - actualDamage);

            if (typeof playSoundEffect === 'function') {
                playSoundEffect(collisionHeartbeatBuffer, Config.HEARTBEAT_VOLUME);
            }

            if (energy <= 0) {
                gameOver();
                return;
            }
        }
    }

    // --- 1. Collisions with Eggs ---
    for (let i = eggs.length - 1; i >= 0; i--) {
        const egg = eggs[i];
        // Skip if egg or its mesh is invalid/disposed
        if (!egg?.mesh || egg.mesh.isDisposed() || !egg.mesh.isEnabled()) {
            if (egg && (!egg.mesh || egg.mesh.isDisposed())) {
                eggs.splice(i, 1);
            } // Cleanup invalid entries
            continue;
        }

        // Use intersectsMesh for collision detection (more reliable than distance)
        if (bunnyCollider.intersectsMesh(egg.mesh, false)) { // false = use bounding box check (faster)
            if (DEBUG_MODE) console.log(`+++ EGG COLLISION! Index: ${i}, Score: ${score}, Energy: ${energy.toFixed(1)} +++`);

            // Determine points/energy multipliers based on whether the egg is in a narrow section
            const eggSection = findSectionAtPosition(egg.mesh.position.z);
            const pointsMultiplier = (eggSection && eggSection.narrowing) ? 2.0 : 1.0;
            const energyMultiplier = (eggSection && eggSection.narrowing) ? 1.5 : 1.0;

            const pointsToAdd = Math.floor(Config.POINTS_PER_EGG * pointsMultiplier);
            const energyToAdd = Config.ENERGY_PER_EGG * energyMultiplier;

            // Update score and energy

            score += pointsToAdd;
            energy = (Math.min(100, energy + energyToAdd)); // Clamp energy at 100

            if (DEBUG_MODE) console.log(`   -> Score: ${score} (+${pointsToAdd}), Energy: ${energy.toFixed(1)} (+${energyToAdd.toFixed(1)})`);

            // Play effects and remove egg
            playParticleEffect("egg", egg.mesh.position);
            playSoundEffect(collectSound);
            egg.light?.dispose();
            egg.mesh.dispose();
            eggs.splice(i, 1); // Remove from array
            // No need to break; player might collect multiple eggs in one frame
        }
    }
    // --- End Egg Collisions ---


    // --- 2. Collisions with Obstacles ---
    for (let i = obstacles.length - 1; i >= 0; i--) {
        const obstacle = obstacles[i];
        // Skip if obstacle or its mesh is invalid/disposed
        if (!obstacle?.mesh || obstacle.mesh.isDisposed() || !obstacle.mesh.isEnabled()) {
            if (obstacle && (!obstacle.mesh || obstacle.mesh.isDisposed())) {
                obstacle.light?.dispose();
                obstacle.mesh?.physicsImpostor?.dispose();
                obstacles.splice(i, 1); // Cleanup
            }
            continue;
        }

        // Use precise intersection check (true) for obstacles
        if (bunnyCollider.intersectsMesh(obstacle.mesh, true)) {
            if (DEBUG_MODE) console.log(`INTERSECTION DETECTED with Obstacle: ${obstacle.mesh.name}`);

            // Check cooldown to prevent rapid damage from single contact
            if (now - lastCollisionTime < minObstacleCollisionInterval) {
                if (DEBUG_MODE) console.log(`   -> Obstacle collision ignored due to cooldown.`);
                continue; // Skip this obstacle if still in cooldown
            }
            lastCollisionTime = now; // Reset cooldown timer

            // Calculate energy loss based on difficulty level
            let damageAmount = Config.ENERGY_LOST_ON_COLLISION;
            if (!obstacle.isDestructible) {
                damageAmount += Config.ENERGY_LOST_INDESTRUCTIBLE_BONUS; // Extra penalty for indestructible
            }

            // Dostosuj obrażenia w zależności od poziomu trudności
            const difficultyModifier = getDifficultyModifier();
            damageAmount *= difficultyModifier;

            if (DEBUG_MODE) console.log(`   -> Energy BEFORE obstacle collision: ${energy.toFixed(1)}`);
            energy = (Math.max(0, energy - damageAmount)); // Apply damage, ensure non-negative
            if (DEBUG_MODE) console.log(`   -> Energy AFTER obstacle collision: ${energy.toFixed(1)} (Damage: ${damageAmount.toFixed(1)})`);

            // Play effects (visual and audio)
            playParticleEffect("obstacleCollision", bunnyCollider.position);
            playSoundEffect(collisionSound);
            playSoundEffect(collisionHeartbeatBuffer, Config.HEARTBEAT_VOLUME); // Play heartbeat on hit

            // Destroy if destructible
            if (obstacle.isDestructible) {
                destroyObstacle(obstacle, i, 0); // 0 points for collision, points for laser destruction
            }

            // Check for game over
            if (energy <= 0) {
                gameOver();
                return; // Exit immediately if game over
            }
            // Break after handling one obstacle collision per frame to enforce cooldown effectively
            break;
        }
    }
    // --- End Obstacle Collisions ---


    // --- 3. Collisions with Attached Wall Cubes ---
    for (let i = wallCubes.length - 1; i >= 0; i--) {
        const cubeData = wallCubes[i];
        // Skip if cube or mesh invalid/disposed
        if (!cubeData?.mesh || cubeData.mesh.isDisposed() || !cubeData.mesh.isEnabled()) {
            if (cubeData && (!cubeData.mesh || cubeData.mesh.isDisposed())) {
                cubeData.mesh?.physicsImpostor?.dispose();
                wallCubes.splice(i, 1); // Cleanup
            }
            continue;
        }

        // Only check collisions with cubes still attached to the wall
        if (cubeData.isAttached && bunnyCollider.intersectsMesh(cubeData.mesh, true)) { // Precise check
            if (DEBUG_MODE) console.log(`INTERSECTION DETECTED with Wall Cube: ${cubeData.mesh.name}`);

            // Check cooldown
            if (now - lastWallCubeCollisionTime < minWallCubeCollisionInterval) {
                if (DEBUG_MODE) console.log(`   -> Wall cube collision ignored due to cooldown.`);
                continue;
            }
            lastWallCubeCollisionTime = now; // Reset cooldown

            // Apply energy penalty
            // Dostosuj obrażenia w zależności od poziomu trudności
            const difficultyModifier = getDifficultyModifier();
            const wallCubePenalty = Config.WALL_CUBE_ENERGY_PENALTY * difficultyModifier;

            if (DEBUG_MODE) console.log(`   -> Energy BEFORE wall cube collision: ${energy.toFixed(1)}`);
            energy = (Math.max(0, energy - wallCubePenalty));
            if (DEBUG_MODE) console.log(`   -> Energy AFTER wall cube collision: ${energy.toFixed(1)} (Penalty: ${wallCubePenalty.toFixed(1)}, Modifier: ${difficultyModifier.toFixed(2)})`);

            // Play effects
            playParticleEffect("obstacleCollision", bunnyCollider.position); // Re-use obstacle effect
            playSoundEffect(collisionSound);
            playSoundEffect(collisionHeartbeatBuffer, Config.HEARTBEAT_VOLUME); // Play heartbeat

            // Detach the cube
            const impostor = cubeData.mesh.getPhysicsImpostor();
            if (impostor) {
                impostor.setMass(Config.WALL_CUBE_DETACH_MASS);
                // Apply impulse away from player
                const impulseDirection = cubeData.mesh.getAbsolutePosition().subtract(bunnyCollider.position).normalize().scale(1.5);
                impostor.applyImpulse(impulseDirection, cubeData.mesh.getAbsolutePosition());
                if (DEBUG_MODE) console.log("   -> Wall cube detached by player.");
            }
            cubeData.isAttached = false; // Mark as detached

            // Check for game over
            if (energy <= 0) {
                gameOver();
                return; // Exit immediately
            }
            // Break after handling one wall cube collision per frame
            break;
        }
    }
    // --- End Wall Cube Collisions ---

    // --- 4. Collisions with Obstacle Fragments ---
    // Znajdź wszystkie fragmenty przeszkód w scenie
    const fragments = scene.meshes.filter(mesh =>
        mesh.name && mesh.name.startsWith("fragment_") &&
        mesh.isEnabled() && !mesh.isDisposed() &&
        mesh.physicsImpostor
    );

    // Sprawdź kolizje z fragmentami
    for (let i = 0; i < fragments.length; i++) {
        const fragment = fragments[i];

        // Sprawdź kolizję z fragmentem
        if (bunnyCollider.intersectsMesh(fragment, true)) { // Dokładne sprawdzenie
            if (DEBUG_MODE) console.log(`INTERSECTION DETECTED with Fragment: ${fragment.name}`);

            // Sprawdź cooldown
            if (now - lastFragmentCollisionTime < minObstacleCollisionInterval) {
                if (DEBUG_MODE) console.log(`   -> Fragment collision ignored due to cooldown.`);
                continue;
            }
            lastFragmentCollisionTime = now; // Zresetuj cooldown

            // Zastosuj karę energii (mniejszą niż za kolizję z pełną przeszkodą)
            // Dostosuj obrażenia w zależności od poziomu trudności
            const difficultyModifier = getDifficultyModifier();
            const fragmentPenalty = Config.FRAGMENT_ENERGY_PENALTY * difficultyModifier;

            if (DEBUG_MODE) console.log(`   -> Energy BEFORE fragment collision: ${energy.toFixed(1)}`);
            energy = (Math.max(0, energy - fragmentPenalty));
            if (DEBUG_MODE) console.log(`   -> Energy AFTER fragment collision: ${energy.toFixed(1)} (Penalty: ${fragmentPenalty.toFixed(1)}, Modifier: ${difficultyModifier.toFixed(2)})`);

            // Efekty
            playParticleEffect("obstacleCollision", bunnyCollider.position);
            playSoundEffect(collisionSound, 0.5); // Cichszy dźwięk dla fragmentów

            // Zastosuj impuls do fragmentu
            const impulseDirection = fragment.position.subtract(bunnyCollider.position).normalize().scale(1.0);
            fragment.physicsImpostor.applyImpulse(impulseDirection, fragment.getAbsolutePosition());

            // Sprawdź game over
            if (energy <= 0) {
                gameOver();
                return; // Natychmiastowe wyjście
            }

            // Przerwij po obsłudze jednej kolizji z fragmentem na klatkę
            break;
        }
    }
    // --- End Fragment Collisions ---
}

/**
 * Zwraca modyfikator obrażeń w zależności od poziomu trudności
 * Poziom trudności jest określany na podstawie średnicy naczynia w aktualnym poziomie
 */
function getDifficultyModifier() {
    // Znajdź aktualny segment naczynia na podstawie poziomu gry
    const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];
    const diameter = currentSegment.diameter;

    // Określ poziom trudności na podstawie średnicy
    if (diameter >= 3.0) {
        // Łatwy poziom - mniejsze obrażenia
        return Config.ENERGY_MODIFIER_EASY;
    } else if (diameter >= 2.0) {
        // Średni poziom - większe obrażenia
        return Config.ENERGY_MODIFIER_MEDIUM;
    } else {
        // Trudny poziom - standardowe obrażenia
        return Config.ENERGY_MODIFIER_HARD;
    }
}


// Update game speed based on tunnel position and narrowing
function updateGameSpeed(deltaTime) { // deltaTime is not directly used here but passed for consistency
    if (!bunnyCollider || tunnelSections.length === 0 || !gameRunning) return;

    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) return;

    const currentSectionIdx = currentSection.index;
    // Progress factor: faster further into the tunnel (closer to index 0)
    const progressFactor = 1.0 + ((tunnelSections.length - 1 - currentSectionIdx) / (tunnelSections.length - 1)) * 0.6; // 1.0 at end, 1.6 at start

    // Narrowing factor based on radius compared to start/min diameters (use radii directly from config/section)
    // Ensure diameters are treated as radii where needed
    const startRadius = Config.TUNNEL_DIAMETER_START; // Radius from config or calculated once
    const minRadius = Config.TUNNEL_DIAMETER_MIN * 2.0; // Effective minimum radius for comparison
    const currentRadius = currentSection.radius; // Radius from the section data

    let narrowingFactor = 0;
    if (startRadius > minRadius) { // Avoid division by zero if tunnel has constant radius
        narrowingFactor = Math.max(0, Math.min(1, (startRadius - currentRadius) / (startRadius - minRadius)));
    }

    // Bernoulli effect: Speed increases in narrower sections
    // Stronger effect boost multiplier
    const bernoulliSpeedBoost = 1.0 + narrowingFactor * 1.2; // Boost up to 2.2x in tightest spots

    // Combine factors and clamp
    const baseSpeedMultiplier = progressFactor * bernoulliSpeedBoost;
    // Update the global speed multiplier used for forward movement
    currentSpeedMultiplier = Math.max(Config.MIN_SPEED_MULTIPLIER,
        Math.min(Config.MAX_SPEED_MULTIPLIER, baseSpeedMultiplier));

    // Adjust dynamic difficulty elements based on this new speed
    adjustDifficultyBasedOnSpeed();
}


// Adjust spawn rates etc based on current speed multiplier
function adjustDifficultyBasedOnSpeed() {
    if (!gameRunning) return;

    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) return;

    // Periodically reset spawn rates to initial values (like at the beginning of the game)
    // Check if we're at a section that's a multiple of SEGMENTS_BETWEEN_RESETS
    const SEGMENTS_BETWEEN_RESETS = 20; // Reset every 20 segments
    const RESET_DURATION = 5000; // Reset lasts for 5 seconds

    // Initialize lastResetSection and resetActive if they don't exist
    if (typeof lastResetSection === 'undefined') {
        lastResetSection = -SEGMENTS_BETWEEN_RESETS; // Start with no reset
    }
    if (typeof resetActive === 'undefined') {
        resetActive = false;
    }
    if (typeof resetEndTime === 'undefined') {
        resetEndTime = 0;
    }

    // Check if we should activate a reset
    if (!resetActive &&
        currentSection.index % SEGMENTS_BETWEEN_RESETS === 0 &&
        currentSection.index !== lastResetSection) {

        // Activate reset
        resetActive = true;
        lastResetSection = currentSection.index;
        resetEndTime = Date.now() + RESET_DURATION;

        if (DEBUG_MODE) console.log(`Resetting spawn rates at section ${currentSection.index} for ${RESET_DURATION}ms`);

        // Set intervals to initial values
        clearInterval(eggSpawnInterval);
        clearInterval(obstacleSpawnInterval);
        clearInterval(wallCubeSpawnInterval);
        clearInterval(redBloodCellSpawnInterval);

        eggSpawnInterval = (setInterval(() => {
            if (gameRunning) spawnEgg();
        }, Config.INITIAL_EGG_SPAWN_INTERVAL));

        obstacleSpawnInterval= (setInterval(() => {
            if (gameRunning) spawnObstacle();
        }, Config.INITIAL_OBSTACLE_SPAWN_INTERVAL));

        wallCubeSpawnInterval = (setInterval(() => {
            if (gameRunning) spawnWallCube();
        }, Config.INITIAL_WALL_CUBE_SPAWN_INTERVAL));

        redBloodCellSpawnInterval = (setInterval(() => {
            if (gameRunning && typeof autoSpawnRedBloodCells === 'function') autoSpawnRedBloodCells();
        }, Config.INITIAL_RED_BLOOD_CELL_SPAWN_INTERVAL));

        // Generate a large group of objects like at the beginning of the game
        if (DEBUG_MODE) console.log("Generating initial-like objects during reset");
        generateInitialObjects();
        generateInitialWallCubes();

        return; // Skip normal adjustment during reset activation
    }

    // Check if reset is active but should end
    if (resetActive && Date.now() >= resetEndTime) {
        resetActive = false;
        if (DEBUG_MODE) console.log(`Reset period ended, returning to normal difficulty adjustment`);
        // Continue with normal adjustment below
    }

    // If reset is still active, don't adjust difficulty
    if (resetActive) {
        return;
    }

    // Normal difficulty adjustment (when not in reset period)
    // Calculate a factor based on current speed relative to min speed
    // Use a power curve to make changes less linear
    // Reduced power from 1.5 to 0.9 to create a more gradual increase in object density
    const speedFactor = Math.pow(currentSpeedMultiplier / Config.MIN_SPEED_MULTIPLIER, 0.9);

    // Adjust frequency based on narrowing
    let eggFrequencyMultiplier = 1.0; // Base interval multiplier
    let obstacleFrequencyMultiplier = 1.0;
    let wallCubeFrequencyMultiplier = 1.0;
    let redBloodCellFrequencyMultiplier = 1.0;

    if (currentSection.narrowing) {
        eggFrequencyMultiplier = 0.8;    // More eggs (shorter interval)
        obstacleFrequencyMultiplier = 1.2; // Fewer obstacles (longer interval) - Keep this distinction? Maybe not? Let's make it 1.0 for more obstacles always.
        obstacleFrequencyMultiplier = 1.0; // *** CHANGED from 1.2 - No reduction in narrows ***
        wallCubeFrequencyMultiplier = 1.1; // Slightly fewer wall cubes
        redBloodCellFrequencyMultiplier = 0.7; // More red blood cells in narrow sections
    }

    // Calculate target intervals, applying speed and narrowing factors, with min caps
    // Increased minimum intervals to ensure more stable object density
    const targetEggInterval = Math.max(400, (Config.EGG_SPAWN_INTERVAL_INITIAL / speedFactor) * eggFrequencyMultiplier); // Increased from 100
    const targetObstacleInterval = Math.max(500, (Config.OBSTACLE_SPAWN_INTERVAL_INITIAL / speedFactor) * obstacleFrequencyMultiplier); // Increased from 150
    const targetWallCubeInterval = Math.max(450, (Config.WALL_CUBE_SPAWN_INTERVAL_INITIAL / speedFactor) * wallCubeFrequencyMultiplier); // Increased from 120
    const targetRedBloodCellInterval = Math.max(800, (Config.RED_BLOOD_CELL_SPAWN_INTERVAL_INITIAL / speedFactor) * redBloodCellFrequencyMultiplier);

    // Check current intervals (handle potential null intervals on first run)
    const currentEggInterval = eggSpawnInterval ? parseInt(eggSpawnInterval._idleTimeout) : Config.EGG_SPAWN_INTERVAL_INITIAL;
    const currentObstacleInterval = obstacleSpawnInterval ? parseInt(obstacleSpawnInterval._idleTimeout) : Config.OBSTACLE_SPAWN_INTERVAL_INITIAL;
    const currentWallCubeInterval = wallCubeSpawnInterval ? parseInt(wallCubeSpawnInterval._idleTimeout) : Config.WALL_CUBE_SPAWN_INTERVAL_INITIAL;
    const currentRedBloodCellInterval = redBloodCellSpawnInterval ? parseInt(redBloodCellSpawnInterval._idleTimeout) : Config.RED_BLOOD_CELL_SPAWN_INTERVAL_INITIAL;

    // Update intervals only if the target differs significantly to avoid frequent resets
    // Increased threshold from 5% to 8% to make transitions smoother and more gradual
    const threshold = 0.08;
    let intervalsUpdated = false;

    if (Math.abs(currentEggInterval - targetEggInterval) / currentEggInterval > threshold) {
        clearInterval(eggSpawnInterval);
        eggSpawnInterval = (setInterval(() => {
            if (gameRunning) spawnEgg();
        }, targetEggInterval));
    }
    if (Math.abs(currentObstacleInterval - targetObstacleInterval) / currentObstacleInterval > threshold) {
        clearInterval(obstacleSpawnInterval);
        obstacleSpawnInterval= (setInterval(() => {
            if (gameRunning) spawnObstacle();
        }, targetObstacleInterval));
    }
    if (Math.abs(currentWallCubeInterval - targetWallCubeInterval) / currentWallCubeInterval > threshold) {
        clearInterval(wallCubeSpawnInterval);
        wallCubeSpawnInterval = (setInterval(() => {
            if (gameRunning) spawnWallCube();
        }, targetWallCubeInterval));
    }
    if (Math.abs(currentRedBloodCellInterval - targetRedBloodCellInterval) / currentRedBloodCellInterval > threshold) {
        clearInterval(redBloodCellSpawnInterval);
        redBloodCellSpawnInterval = (setInterval(() => {
            if (gameRunning && typeof autoSpawnRedBloodCells === 'function') autoSpawnRedBloodCells();
        }, targetRedBloodCellInterval));
    }
}


// Increase base difficulty over time
function increaseGameDifficulty() {
    if (!gameRunning) return;

    // Gradually increase the absolute base speed limit
    Config.BASE_SPEED = Math.min(Config.MAX_BASE_SPEED, Config.BASE_SPEED + Config.BASE_SPEED_INCREASE);

    // Other difficulty increases (like fleeing speed) can be added here
    Config.EGG_FLEE_SPEED = Math.min(Config.EGG_FLEE_SPEED + 0.005, 0.3); // Slower increase
    Config.EGG_FLEE_DETECTION_RADIUS = Math.min(Config.EGG_FLEE_DETECTION_RADIUS + 0.1, 12); // Slower increase

    // Small energy bonus for surviving longer
    energy = Math.min(100, energy + 1);
    updateHUD();

    if (DEBUG_MODE) console.log(`Difficulty Increased: BaseSpeed=${Config.BASE_SPEED.toFixed(4)}, FleeSpeed=${Config.EGG_FLEE_SPEED.toFixed(3)}, FleeRadius=${Config.EGG_FLEE_DETECTION_RADIUS.toFixed(2)}`);

    // Spawn intervals are handled by adjustDifficultyBasedOnSpeed now
}

/**
 * Initializes or restarts the game for the next level.
 * Resets state, calculates difficulty, recreates the tunnel, resets the player,
 * and starts game intervals.
 */
// --- START MODIFIED section in gameLogic.js ---
async function startGame() {
    console.log("=== STARTING GAME ===");

    // RESET FLAG DLA KRWINEK
    window.bloodCellsInitialized = false;

    // KRYTYCZNE: Sprawdź czy UI jest zainicjalizowane
    if (!uiInitialized) {
        console.log("UI not initialized, initializing now...");
        getUIElementReferences();
        if (!uiInitialized) {
            console.error("FATAL: Cannot start game without UI");
            return;
        }
    }

    // KRYTYCZNE: Ukryj wszystkie menu i pokaż loading
    console.log("Setting up UI for game start...");

    // Ukryj start screen
    if (uiElements.startScreen) {
        uiElements.startScreen.style.display = 'none';
    }

    // Pokaż loading screen
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.style.display = 'flex';
        loadingScreen.style.opacity = '1';
    }

    // Ustaw tekst loading
    const loadingText = document.getElementById('loadingText');
    if (loadingText) {
        loadingText.textContent = 'Starting Game...';
    }

    // Reset game running state
    gameRunning = false;

    if (DEBUG_MODE) console.log("startGame function called - beginning game initialization");

    // Clean up demo scene if it's active
    if (typeof window.isDemoActive === 'function' && window.isDemoActive()) {
        if (DEBUG_MODE) console.log("Cleaning up demo scene before starting game");
        if (typeof window.cleanupDemoScene === 'function') {
            window.cleanupDemoScene(window.scene || scene);
        }
    }

    // Only increment level if called from completeLevel (via setTimeout)
    // We can detect this by checking if the caller is setTimeout
    // NOTE: This stack trace check might be unreliable in some environments/minification.
    // Consider passing an explicit flag if this becomes an issue.
    let calledFromCompleteLevel = false;
    try {
        calledFromCompleteLevel = new Error().stack.includes('completeLevel');
    } catch (e) {
        if (DEBUG_MODE) console.warn("Could not determine call stack for level increment.");
    }

    if (calledFromCompleteLevel && gameLevel > 0) {
        // Increment level only when called from completeLevel
        gameLevel = ((gameLevel % 18) + 1); // Increment level and wrap around after level 18
    } else if (gameLevel <= 0) { // Ensure level is at least 1
        gameLevel = 1;
    }
    if (DEBUG_MODE) console.log(`startGame() called for Level ${gameLevel}`);

    // --- IMPROVED Scene Check ---
    // Check if scene exists and wait for it to be ready
    // First try to get scene from window.scene (created in main.js)
    if (!scene && window.scene) {
        scene = window.scene;
        if (DEBUG_MODE) console.log("Retrieved scene from window.scene");
    }

    if (!scene) {
        console.error("FATAL: Scene is not initialized or invalid before startGame logic!");
        // Try to get the scene from the engine
        try {
            if (window.engine && window.engine.scenes && window.engine.scenes.length > 0) {
                // Use the first scene
                scene = window.engine.scenes[0];
                if (DEBUG_MODE) console.log("Retrieved scene from window.engine");
            } else if (engine && engine.scenes && engine.scenes.length > 0) {
                // Use the first scene from global engine
                scene = engine.scenes[0];
                if (DEBUG_MODE) console.log("Retrieved scene from global engine");
            } else {
                // Create a new scene as a last resort
                const currentEngine = window.engine || engine;
                if (currentEngine) {
                    scene = new BABYLON.Scene(currentEngine);
                    window.scene = scene; // Store in window for future use
                    if (DEBUG_MODE) console.log("Created new scene as fallback");
                }
            }
        } catch (sceneError) {
            console.error("Failed to recover scene:", sceneError);
        }

        // If we still don't have a valid scene, show error and abort
        if (!scene || typeof scene.isReady !== 'function') {
            // Optionally show an error message to the user
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = 'position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); padding:20px; background-color:rgba(200,0,0,0.8); color:white; border:2px solid darkred; z-index: 1000; text-align: center;';
            errorDiv.innerHTML = `<h2>Error</h2><p>Game scene failed to initialize. Please refresh.</p>`;
            document.body.appendChild(errorDiv);
            gameRunning = false;
            return; // Stop execution
        }
    }

    // Wait for scene to be ready
    if (scene && typeof scene.isReady === 'function' && !scene.isReady()) {
        if (DEBUG_MODE) console.log("Waiting for scene to be ready...");
        await new Promise(resolve => {
            scene.executeWhenReady(() => {
                if (DEBUG_MODE) console.log("Scene is now ready");
                resolve();
            });
        }).catch(e => {
            console.error("Error waiting for scene:", e);
            // Continue anyway as a best effort
        });
    }
    // --- END ADDED Scene Check ---


    // --- Calculate Difficulty Parameters based on gameLevel ---
    // ... (rest of the difficulty calculation remains the same) ...
    // Ensure gameLevel is within valid range (1-18)
    gameLevel = (Math.max(1, Math.min(18, gameLevel)));

    // Set tunnel diameter based on coronary segment for current level
    const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];
    Config.TUNNEL_DIAMETER_START = currentSegment.diameter;

    // Adjust minimum diameter and end diameter proportionally
    const diameterRatio = currentSegment.diameter / 5.0; // Ratio compared to default 5.0
    Config.TUNNEL_DIAMETER_MIN = 0.4 * diameterRatio;
    Config.TUNNEL_DIAMETER_END = 0.2 * diameterRatio;

    if (DEBUG_MODE) {
        console.log(`Level ${gameLevel}: ${currentSegment.segment} - ${currentSegment.name}`);
        console.log(`Average Diameter: ${currentSegment.diameter}mm, Min: ${Config.TUNNEL_DIAMETER_MIN.toFixed(2)}, End: ${Config.TUNNEL_DIAMETER_END.toFixed(2)}`);
        console.log(`Note: Actual diameter will vary by ±15% from these average values`);
    }

    // Tunnel Segments: Longer tunnels per level
    const calculatedSegments = Config.BASE_TUNNEL_SEGMENTS + (gameLevel - 1) * Config.SEGMENTS_PER_LEVEL;
    Config.TUNNEL_SEGMENTS = Math.max(11, Math.floor(calculatedSegments)); // Ensure >= 11 segments
    if (calculatedSegments < 11 && DEBUG_MODE) {
        console.warn(`Calculated segments (${calculatedSegments}) too low for level ${gameLevel}. Clamped to ${Config.TUNNEL_SEGMENTS}.`);
    }

    // Initial Speed: Starts slower in early levels and gradually increases
    // Reduced progression rate for early levels
    const speedProgressionFactor = gameLevel <= 3 ? 0.002 : 0.004; // Slower progression for first 3 levels
    Config.BASE_SPEED = Math.min(Config.MAX_BASE_SPEED, Config.INITIAL_BASE_SPEED + (gameLevel - 1) * speedProgressionFactor);

    // Reduced speed multiplier for early levels
    const multiplierProgressionFactor = gameLevel <= 3 ? 0.03 : 0.06; // Slower progression for first 3 levels
    Config.MIN_SPEED_MULTIPLIER = Math.min(Config.MAX_SPEED_MULTIPLIER * 0.8, Config.INITIAL_MIN_SPEED_MULTIPLIER + (gameLevel - 1) * multiplierProgressionFactor);

    // Spawn Intervals: Decrease (faster spawn) each level, with minimum caps
    // Slower spawn rates for early levels
    const eggIntervalReduction = gameLevel <= 3 ? 40 : 75; // Less reduction for first 3 levels
    const obstacleIntervalReduction = gameLevel <= 3 ? 50 : 100; // Less reduction for first 3 levels
    const wallCubeIntervalReduction = gameLevel <= 3 ? 25 : 50; // Less reduction for first 3 levels

    Config.EGG_SPAWN_INTERVAL_INITIAL = Math.max(150, Config.INITIAL_EGG_SPAWN_INTERVAL - (gameLevel - 1) * eggIntervalReduction);
    Config.OBSTACLE_SPAWN_INTERVAL_INITIAL = Math.max(250, Config.INITIAL_OBSTACLE_SPAWN_INTERVAL - (gameLevel - 1) * obstacleIntervalReduction);
    Config.WALL_CUBE_SPAWN_INTERVAL_INITIAL = Math.max(200, Config.INITIAL_WALL_CUBE_SPAWN_INTERVAL - (gameLevel - 1) * wallCubeIntervalReduction);

    if (DEBUG_MODE) {
        console.log(`Level ${gameLevel} Params: Segments=${Config.TUNNEL_SEGMENTS}, BaseSpeed=${Config.BASE_SPEED.toFixed(4)}, MinMult=${Config.MIN_SPEED_MULTIPLIER.toFixed(2)}`);
        console.log(`Level ${gameLevel} Intervals: Egg=${Config.EGG_SPAWN_INTERVAL_INITIAL}, Obst=${Config.OBSTACLE_SPAWN_INTERVAL_INITIAL}, WC=${Config.WALL_CUBE_SPAWN_INTERVAL_INITIAL}`);
    }


    // --- End Difficulty Calculation ---


    // Resume Audio Context & Load Sounds (if needed)
    let soundsLoadedSuccessfully = false;
    await initializeAudioContext(); // Ensure context exists
    if (audioContext && audioContext.state === 'suspended') {
        try {
            if (DEBUG_MODE) console.log("Attempting to resume AudioContext from startGame...");
            await audioContext.resume();
            if (DEBUG_MODE) console.log("AudioContext resumed successfully. State:", audioContext.state);
            // Load sounds now
            if (typeof initAudioAndLoadSounds === 'function') {
                if (DEBUG_MODE) console.log("Loading sounds after context resume...");
                soundsLoadedSuccessfully = await initAudioAndLoadSounds();
            } else {
                console.error("initAudioAndLoadSounds function not found!");
            }
        } catch (e) {
            if (DEBUG_MODE) console.warn("AudioContext resume failed in startGame (maybe still needs interaction):", e.message);
            soundEnabled = false; // Disable sound if resume fails here
        }
    } else if (audioContext && audioContext.state === 'running') {
        if (DEBUG_MODE) console.log("AudioContext already running on startGame.");
        // Still try loading sounds in case they failed earlier or this is the first run
        if (typeof initAudioAndLoadSounds === 'function') {
            if (DEBUG_MODE) console.log("Loading sounds (context already running)...");
            soundsLoadedSuccessfully = await initAudioAndLoadSounds();
        } else {
            console.error("initAudioAndLoadSounds function not found!");
        }
    } else {
        if (DEBUG_MODE) console.warn("AudioContext not available or in unusable state during startGame.");
        soundEnabled = false; // Ensure sound is disabled
    }
    if (DEBUG_MODE) console.log(`Sounds loaded status: ${soundsLoadedSuccessfully}`);


    // Reset game state variables (except score which is cumulative across levels)
    // score is not reset here to make it cumulative across levels
    energy = Config.INITIAL_ENERGY;
    ammo = Config.MAX_AMMO;
    currentSpeedMultiplier = Config.MIN_SPEED_MULTIPLIER; // Use calculated value
    lastShotTime = 0;
    lastCollisionTime = 0;
    wallCollisionCooldown = 0;
    lastWallCubeCollisionTime = 0;
    Object.keys(keysPressed).forEach(key => {
        keysPressed[key] = false;
    });

    // Clear existing dynamic objects
    const clearArray = (arr) => {
        arr.forEach(item => {
            item.light?.dispose();
            item.mesh?.physicsImpostor?.dispose();
            item.mesh?.dispose();
        });
        arr.length = 0;
    };
    clearArray(eggs);
    clearArray(obstacles);
    clearArray(lasers);
    clearArray(wallCubes);

    // Wyczyść krwinki czerwone
    // Próbuj różne sposoby dostępu do funkcji clearOptimizedBloodCells lub clearBloodCells
    if (typeof clearOptimizedBloodCells === 'function') {
        clearOptimizedBloodCells();
    } else if (typeof window.clearOptimizedBloodCells === 'function') {
        window.clearOptimizedBloodCells();
    } else if (typeof globalThis.clearOptimizedBloodCells === 'function') {
        globalThis.clearOptimizedBloodCells();
    } else if (typeof clearBloodCells === 'function') {
        // Fallback do starej implementacji
        clearBloodCells();
    } else if (typeof window.clearBloodCells === 'function') {
        window.clearBloodCells();
    } else if (typeof globalThis.clearBloodCells === 'function') {
        globalThis.clearBloodCells();
    } else {
        console.warn("Blood cell cleanup functions not found, skipping blood cell cleanup");
    }


    if (DEBUG_MODE) console.log("Cleared old game objects.");

    // --- Recreate Tunnel for the current level ---
    if (DEBUG_MODE) console.log(`Recreating tunnel for level ${gameLevel}...`);
    // updateLoadingProgress(`Building Level ${gameLevel}...`, 50); // Can't call this easily here, maybe show message on HUD?
    if (Config.TUNNEL_SEGMENTS <= 10) {
        console.error(`FATAL: Invalid segment count (${Config.TUNNEL_SEGMENTS}) calculated for level ${gameLevel}. Cannot create tunnel.`);
        showOverlay(uiElements.gameOverScreen); // Or show a specific error message
        // Optionally reset level
        gameLevel = Math.max(1, gameLevel - 1);
        return; // Stop startGame
    }

    // --- ADDED: Double Check Scene validity just before tunnel creation ---
    if (!scene || typeof scene.isReady !== 'function') {
        console.error("FATAL: Scene became invalid before tunnel creation!");
        showOverlay(uiElements.gameOverScreen);
        return;
    }
    // Czekanie na gotowość sceny
    if (scene && typeof scene.isReady === 'function' && !scene.isReady()) {
        await new Promise(resolve => scene.executeWhenReady(resolve));
    }
    // --- END ADDED Check ---
    // Tworzenie tunelu z lepszym logowaniem i obsługą błędów
    let tunnelCreationResult = null;
    try {
        tunnelCreationResult = await createTunnel(Config.TUNNEL_SEGMENTS);
        const currentTunnelMesh = tunnelMesh;

        // Check both the return value and the global tunnelMesh variable
        if (!tunnelCreationResult || !currentTunnelMesh) {
            console.error(`FATAL: Failed to create tunnel for level ${gameLevel}!`);
            console.log(`Tunnel creation diagnostics: Return value: ${tunnelCreationResult ? "valid" : "null/undefined"}, Global tunnelMesh: ${currentTunnelMesh ? "valid" : "null/undefined"}`);

            // Try recovery attempt
            if (!currentTunnelMesh && tunnelCreationResult) {
                console.log("Attempting to recover using the return value from createTunnel");
                tunnelMesh = tunnelCreationResult;
            }

            // Final check after recovery attempt
            if (!getTunnelMesh()) {
                // Handle error - Maybe revert level? Show fatal error?
                gameLevel = (Math.max(1, gameLevel - 1)); // Revert level increment
                showOverlay(uiElements.gameOverScreen); // Show game over as we can't proceed
                // Optionally display a specific error message on game over screen
                const errorMsg = uiElements.gameOverScreen?.querySelector('#gameOverMessage'); // Assume element exists
                if (errorMsg) errorMsg.textContent = "Error building level tunnel.";
                return; // Stop startGame execution
            } else {
                console.log("Tunnel mesh recovered successfully");
            }
        } else {
            // Tunnel created successfully
            fixTunnelLighting(scene);
            console.log(`Tunnel created successfully for Level ${gameLevel}.`);
        }
    } catch (tunnelError) {
        console.error("Critical error during tunnel creation:", tunnelError);
        showOverlay(uiElements.gameOverScreen);
        const errorMsg = uiElements.gameOverScreen?.querySelector('#gameOverMessage');
        if (errorMsg) errorMsg.textContent = "Critical error building level.";
        return;
    }

    // Only log success if we have a valid tunnel
    if (tunnelMesh) {
        // updateLoadingProgress(`Ready!`, 100);
        if (DEBUG_MODE) console.log(`Tunnel created successfully for Level ${gameLevel}. Sections: ${tunnelSections.length}`);
    }
    // --- End Tunnel Recreation ---

    // Enable physics for the scene
    if (scene && typeof BABYLON.PhysicsImpostor !== 'undefined') {
        try {
            // Create a new physics engine with default gravity
            const gravityVector = new BABYLON.Vector3(0, -9.81, 0);
            scene.enablePhysics(gravityVector, new BABYLON.CannonJSPlugin());
            if (DEBUG_MODE) console.log("Physics enabled for the scene");
        } catch (physicsError) {
            console.error("Error enabling physics:", physicsError);
            // Continue without physics - game can still function
        }
    }

    // Create bunny if it doesn't exist yet
    if (!bunnyCollider) {
        if (DEBUG_MODE) console.log("Creating bunny for the first time...");
        try {
            await callCreateBunny(scene);
            if (!bunnyCollider) {
                throw new Error("Failed to create bunny");
            }
            // Configure camera for the bunny - force recreation to ensure proper mode
            console.log("Configuring camera with mode:", cameraMode);
            configureCamera(scene, true); // Force recreation

            // Initialize any pending object pools now that creation functions should be available
            if (typeof window.initializePendingObjectPools === 'function') {
                if (DEBUG_MODE) console.log("Initializing pending object pools...");
                const result = window.initializePendingObjectPools();
                if (DEBUG_MODE) console.log("Pending object pools initialization result:", result);
            } else {
                if (DEBUG_MODE) console.log("initializePendingObjectPools function not available");
            }
        } catch (error) {
            console.error("Error creating bunny:", error);
            showOverlay(uiElements.gameOverScreen);
            return;
        }
    }

    // Reset player position based on the *new* tunnel
    if (bunnyCollider && tunnelSections.length > 1) {
        const startSectionIndex = Math.max(0, tunnelSections.length - 2); // Start near the end
        const startSection = tunnelSections[startSectionIndex];
        const lookSection = tunnelSections[Math.max(0, startSectionIndex - 1)];
        // Position at center point of start section instead of end point to ensure player is within visible tunnel
        bunnyCollider.position = startSection.centerPoint.clone();

        // Calculate direction player should initially face (along the tunnel backwards)
        const forwardDirection = startSection.endPoint.subtract(lookSection.endPoint).normalize();
        if (forwardDirection.lengthSquared() < 0.1) {
            forwardDirection.copyFrom(BABYLON.Axis.Z.negate());
        } // Fallback
        let upVector = BABYLON.Axis.Y;
        if (Math.abs(BABYLON.Vector3.Dot(forwardDirection, BABYLON.Axis.Y)) > 0.95) {
            upVector = BABYLON.Axis.X;
        }

        try {
            bunnyCollider.rotationQuaternion = BABYLON.Quaternion.FromLookDirectionLH(forwardDirection, upVector);
        } catch (e) {
            console.error("Error setting player rotation on start:", e);
            bunnyCollider.rotationQuaternion = BABYLON.Quaternion.Identity();
        }
        // Update current section index based on reset position
        const currentSec = findSectionAtPosition(bunnyCollider.position.z);
        if (currentSec) {
            // findSectionAtPosition already updates currentSectionIndex
        } else {
            currentSectionIndex = startSectionIndex;
        }
        if (DEBUG_MODE) console.log(`Player position reset for new tunnel. Current Index: ${currentSectionIndex}.`);
    } else {
        console.error("Cannot reset player position - tunnel invalid after creation or bunny missing!");
        showOverlay(uiElements.gameOverScreen); // End game if we can't place the player
        return;
    }

    // Clean up start screen elements
    try {
        // Remove start screen background and decorations
        const startScreenBackground = scene.getMeshByName("startScreenBackground");
        if (startScreenBackground) {
            startScreenBackground.dispose();
        }

        // Remove decorative spheres
        for (let i = 0; i < 20; i++) {
            const sphere = scene.getMeshByName(`startSphere${i}`);
            if (sphere) {
                sphere.dispose();
            }
        }

        // Keep the logo plane for use in the game
        // We'll just move it out of view
        const logoPlane = scene.getMeshByName("logoPlane");
        if (logoPlane) {
            logoPlane.position.y = 1000; // Move far away
        }
    } catch (error) {
        console.warn("Error cleaning up start screen elements:", error);
    }

    // Update UI (including Level)
    hideOverlay(uiElements.startWindow);
    hideOverlay(uiElements.startScreen);
    hideOverlay(uiElements.gameOverScreen);
    hideOverlay(uiElements.loadingScreen);

    // Show HUD
    const hudElement = document.getElementById('hud');
    if (hudElement) {
        hudElement.style.display = 'block';
    }

    updateHUD(); // Updates Level display
    if (DEBUG_MODE) console.log("UI updated for game start.");

    // Spawn initial objects for the level
    if (typeof generateInitialObjects === 'function') {
        generateInitialObjects();
        console.log("Initial objects generated");
    } else {
        console.error("generateInitialObjects function is not defined. Make sure objects.js is loaded properly.");
    }

    if (typeof generateInitialWallCubes === 'function') {
        generateInitialWallCubes();
        console.log("Initial wall cubes generated");
    } else {
        console.error("generateInitialWallCubes function is not defined. Make sure objects.js is loaded properly.");
    }

    // ===== ZMIENIONE: Zoptymalizowana inicjalizacja obiektów biologicznych =====
    console.log("Initializing optimized biological objects...");

    // 1. Wyczyść stare obiekty biologiczne
    if (typeof cleanupBiologicalManager === 'function') {
        cleanupBiologicalManager();
    }

    // Wyczyść stare tablice dla kompatybilności
    if (Array.isArray(whiteBloodCells)) {
        whiteBloodCells.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        whiteBloodCells.length = 0;
    }

    if (Array.isArray(yellowLeukocytes)) {
        yellowLeukocytes.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        yellowLeukocytes.length = 0;
    }

    // 2. Inicjalizuj nowy system biologiczny
    setTimeout(() => {
        console.log("Initializing BiologicalManager...");
        if (typeof initializeBiologicalManager === 'function') {
            try {
                initializeBiologicalManager(scene);
                console.log("BiologicalManager initialized successfully");
            } catch (error) {
                console.error("Error initializing BiologicalManager:", error);
                // Fallback do starych funkcji
                fallbackToLegacyBloodSystem();
            }
        } else {
            console.warn("BiologicalManager not available, using legacy system");
            fallbackToLegacyBloodSystem();
        }
    }, 1000); // 1 sekunda opóźnienia

    // 4. Na końcu generuj zmiany miażdżycowe
    setTimeout(() => {
        console.log("Creating atherosclerotic plaques...");
        if (typeof generateAtheroscleroticPlaques === 'function' && tunnelSections && tunnelSections.length > 0) {
            try {
                generateAtheroscleroticPlaques(tunnelSections, gameLevel, scene);
                console.log(`Atherosclerotic plaques generated for level ${gameLevel}`);
            } catch (error) {
                console.error("Error generating atherosclerotic plaques:", error);
            }
        } else {
            console.warn("generateAtheroscleroticPlaques function not available or tunnel sections missing");
        }
    }, 2000); // 2 sekundy opóźnienia

    // 5. Sprawdź po 3 sekundach, czy wszystkie obiekty zostały utworzone i ukryj ekran ładowania
    setTimeout(() => {
        console.log("Final initialization check completed - ensuring loading screen is hidden");
        // Make sure the loading screen is hidden
        if (typeof hideOverlay === 'function' && uiElements && uiElements.loadingScreen) {
            hideOverlay(uiElements.loadingScreen);
            console.log("Loading screen hidden after final initialization check");
        } else {
            // Direct approach to hide loading screen if uiElements.loadingScreen is not available
            const loadingScreenElement = document.getElementById('loadingScreen');
            if (loadingScreenElement) {
                loadingScreenElement.style.opacity = '0';
                setTimeout(() => {
                    loadingScreenElement.style.display = 'none';
                }, 300);
                console.log("Loading screen hidden directly (fallback method)");
            } else {
                console.warn("Could not find loading screen element to hide");
            }
        }

        // Show HUD again to ensure it's visible
        const hudElement = document.getElementById('hud');
        if (hudElement) {
            hudElement.style.display = 'block';
        }

        // Update HUD to ensure all values are displayed correctly
        if (typeof updateHUD === 'function') {
            updateHUD();
        }

        // Set game as running
        gameRunning = true;
    }, 3000); // 3 sekundy opóźnienia dla sprawdzenia

    // Start game logic intervals using current Config values
    clearInterval(eggSpawnInterval);
    clearInterval(obstacleSpawnInterval);
    clearInterval(wallCubeSpawnInterval);
    clearInterval(redBloodCellSpawnInterval);
    clearInterval(speedIncreaseInterval);
    clearInterval(ammoRegenInterval);
    eggSpawnInterval = (setInterval(() => {
        if (gameRunning) spawnEgg();
    }, Config.EGG_SPAWN_INTERVAL_INITIAL));
    obstacleSpawnInterval = (setInterval(() => {
        if (gameRunning) spawnObstacle();
    }, Config.OBSTACLE_SPAWN_INTERVAL_INITIAL));
    wallCubeSpawnInterval = (setInterval(() => {
        if (gameRunning) spawnWallCube();
    }, Config.WALL_CUBE_SPAWN_INTERVAL_INITIAL));
    redBloodCellSpawnInterval = (setInterval(() => {
        if (gameRunning && typeof autoSpawnRedBloodCells === 'function') autoSpawnRedBloodCells();
    }, Config.RED_BLOOD_CELL_SPAWN_INTERVAL_INITIAL));
    speedIncreaseInterval = (setInterval(() => {
        if (gameRunning) increaseGameDifficulty();
    }, Config.DIFFICULTY_INCREASE_INTERVAL));
    ammoRegenInterval = (setInterval(() => {
        if (gameRunning && ammo < Config.MAX_AMMO) {
            ammo = Math.min(ammo + 1, Config.MAX_AMMO);
            updateHUD();
        }
    }, Config.AMMO_REGEN_INTERVAL));
    if (DEBUG_MODE) console.log("Game intervals started.");

    // Set focus and prepare game state
    // Get canvas element directly if the imported canvas is null
    const canvasElement = canvas || document.getElementById('gameCanvas');
    if (canvasElement) canvasElement.focus();

    cameraReachedPlayer = false;
    gameRunning = true;

    // Ensure gameState is properly set for the new level
    if (window.gameState) {
        window.gameState.running = true;
        window.gameState.paused = false;
        window.gameState.level = gameLevel;

        // Sync the updated gameState with global variables
        if (typeof syncToGlobals === 'function') {
            syncToGlobals();
        }
    }

    // Reset camera position to ensure it's properly positioned relative to the player
    // This will help the camera reach the player in the game loop
    cameraReachedPlayer = false; // Reset camera flag
    if (typeof resetCameraPosition === 'function') {
        setTimeout(() => {
            resetCameraPosition();
            console.log("Camera position reset after game start");
            // Force camera to reach player immediately for testing
            if (bunnyCollider && camera) {
                cameraReachedPlayer = true;
                console.log("Camera reached player flag set to true");
            }
        }, 500); // Increased delay to ensure bunnyCollider is properly positioned
    }

    // Ensure music plays when the game starts
    if (typeof ensureMusicPlays === 'function') {
        ensureMusicPlays();
    } else if (typeof playBackgroundMusic === 'function' && musicEnabled) {
        playBackgroundMusic();
    }

    // NA KOŃCU funkcji startGame(), dodaj:
    setTimeout(() => {
        console.log("=== FINALIZING GAME START ===");

        // Ukryj loading screen
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
            console.log("Loading screen hidden");
        }

        // Pokaż HUD
        const hud = document.getElementById('hud');
        if (hud) {
            hud.style.display = 'block';
            console.log("HUD shown");
        }

        // Upewnij się, że gameRunning jest true
        gameRunning = true;

        // Inicjalizuj input handling
        if (typeof initializeInputHandling === 'function') {
            initializeInputHandling();
        }

        console.log("=== GAME START COMPLETE ===");
    }, 3000);

    if (DEBUG_MODE) console.log(`startGame() finished. Level ${gameLevel} prepared. Waiting for camera to reach player.`);
}


// Pause game and show menu
function pauseGame() {
    console.log("=== PAUSE GAME ===");

    if (!gameRunning) return;

    gameRunning = false;

    // Pokaż menu pauzy
    const pauseMenu = createPauseMenu();
    document.body.appendChild(pauseMenu);
}

function createPauseMenu() {
    const pauseMenu = document.createElement('div');
    pauseMenu.id = 'pauseMenu';
    pauseMenu.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        color: white;
    `;

    const title = document.createElement('h2');
    title.textContent = 'Game Paused';
    title.style.marginBottom = '20px';
    pauseMenu.appendChild(title);

    // Continue button
    const continueBtn = document.createElement('button');
    continueBtn.textContent = 'Continue Game';
    continueBtn.style.cssText = 'margin: 10px; padding: 10px 20px; font-size: 16px;';
    continueBtn.onclick = () => {
        pauseMenu.remove();
        gameRunning = true;
    };
    pauseMenu.appendChild(continueBtn);

    // Main menu button
    const mainMenuBtn = document.createElement('button');
    mainMenuBtn.textContent = 'Main Menu';
    mainMenuBtn.style.cssText = 'margin: 10px; padding: 10px 20px; font-size: 16px;';
    mainMenuBtn.onclick = () => {
        pauseMenu.remove();
        returnToMainMenu();
    };
    pauseMenu.appendChild(mainMenuBtn);

    return pauseMenu;
}

function returnToMainMenu() {
    console.log("=== RETURNING TO MAIN MENU ===");

    gameRunning = false;

    // Ukryj HUD
    const hud = document.getElementById('hud');
    if (hud) {
        hud.style.display = 'none';
    }

    // Pokaż start screen
    if (uiElements.startScreen) {
        uiElements.startScreen.style.display = 'flex';
    }

    // Restart demo scene
    if (typeof window.initDemoScene === 'function') {
        window.initDemoScene(scene);
    }
}

// NOWA FUNKCJA: updatePlayerMovement
function updatePlayerMovement(deltaTime) {
    if (!bunnyCollider || !gameRunning) return;

    const moveSpeed = Config.BASE_SPEED * currentSpeedMultiplier * deltaTime * 100;
    let moved = false;

    // Ruch w lewo/prawo
    if (keysPressed['ArrowLeft'] || keysPressed['KeyA']) {
        bunnyCollider.position.x -= moveSpeed;
        moved = true;
    }
    if (keysPressed['ArrowRight'] || keysPressed['KeyD']) {
        bunnyCollider.position.x += moveSpeed;
        moved = true;
    }

    // Ruch góra/dół
    if (keysPressed['ArrowUp'] || keysPressed['KeyW']) {
        bunnyCollider.position.y += moveSpeed;
        moved = true;
    }
    if (keysPressed['ArrowDown'] || keysPressed['KeyS']) {
        bunnyCollider.position.y -= moveSpeed;
        moved = true;
    }

    // Automatyczny ruch do przodu
    const forwardSpeed = Config.BASE_SPEED * currentSpeedMultiplier * deltaTime * 50;
    bunnyCollider.position.z -= forwardSpeed; // Ruch w kierunku -Z

    // Ogranicz pozycję gracza do tunelu
    if (typeof constrainPositionToTunnel === 'function') {
        bunnyCollider.position = constrainPositionToTunnel(bunnyCollider.position, 0.9);
    }

    if (moved && DEBUG_MODE) {
        console.log("Player moved to:", bunnyCollider.position);
    }
}

// Complete the current level and proceed to the next one
function completeLevel() {
    if (!gameRunning) return; // Prevent multiple calls
    if (DEBUG_MODE) console.log("completeLevel() called - Player completed the level!");
    gameRunning = false;

    // Ensure gameState is updated to reflect level completion
    if (window.gameState) {
        window.gameState.running = false;
        window.gameState.levelCompleted = true;
        // Don't set paused to true, as we want to start the next level automatically
    }

    // Mark the current level as completed
    saveCompletedLevel(gameLevel);

    // Hide HUD
    const hudElement = document.getElementById('hud');
    if (hudElement) {
        hudElement.style.display = 'none';
    }

    // Show a brief "Level Complete" message
    const levelCompleteMsg = document.createElement('div');
    levelCompleteMsg.id = 'levelCompleteMessage';
    levelCompleteMsg.style.position = 'absolute';
    levelCompleteMsg.style.top = '50%';
    levelCompleteMsg.style.left = '50%';
    levelCompleteMsg.style.transform = 'translate(-50%, -50%)';
    levelCompleteMsg.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
    levelCompleteMsg.style.color = 'white';
    levelCompleteMsg.style.padding = '20px';
    levelCompleteMsg.style.borderRadius = '10px';
    levelCompleteMsg.style.fontSize = '24px';
    levelCompleteMsg.style.textAlign = 'center';
    levelCompleteMsg.style.zIndex = '100';
    levelCompleteMsg.textContent = `Level ${gameLevel} Complete!`;
    document.body.appendChild(levelCompleteMsg);

    // Save high score before moving to next level
    saveHighScore();

    // Remove the message and start the next level after a delay
    setTimeout(() => {
        if (levelCompleteMsg.parentNode) {
            levelCompleteMsg.parentNode.removeChild(levelCompleteMsg);
        }

        // Ensure gameState is ready for the next level
        if (window.gameState) {
            window.gameState.running = true;
            window.gameState.paused = false;
            window.gameState.levelCompleted = false;
        }

        startGame(); // This will increment the level and start the next one
    }, 2000);
}

// Save the high score to localStorage
function saveHighScore() {
    const currentHighScore = getHighScore();
    if (score > currentHighScore) {
        try {
            // Store as a JSON string to ensure compatibility with extensions that might parse it
            localStorage.setItem('bunnyTunnelHighScore', JSON.stringify({score: score}));
            if (DEBUG_MODE) console.log(`New high score saved: ${score}`);
        } catch (e) {
            console.error("Error saving high score:", e);
        }
    }
}

// Get the high score from localStorage
function getHighScore() {
    try {
        const highScoreData = localStorage.getItem('bunnyTunnelHighScore');
        if (!highScoreData) return 0;

        // Try to parse as JSON first (new format)
        try {
            const parsedData = JSON.parse(highScoreData);
            return parsedData.score || 0;
        } catch (jsonError) {
            // Fallback for old format (direct number string)
            return parseInt(highScoreData) || 0;
        }
    } catch (e) {
        console.error("Error getting high score:", e);
        return 0;
    }
}

// Track completed levels in localStorage
function saveCompletedLevel(level) {
    try {
        // Get existing completed levels
        const completedLevels = getCompletedLevels();

        // Add the current level if not already in the array
        if (!completedLevels.includes(level)) {
            completedLevels.push(level);

            // Store back to localStorage
            localStorage.setItem('bunnyTunnelCompletedLevels', JSON.stringify(completedLevels));
            if (DEBUG_MODE) console.log(`Level ${level} marked as completed`);
        }
    } catch (e) {
        console.error("Error saving completed level:", e);
    }
}

// Get array of completed levels from localStorage
function getCompletedLevels() {
    try {
        const completedLevelsData = localStorage.getItem('bunnyTunnelCompletedLevels');
        if (!completedLevelsData) return [];

        return JSON.parse(completedLevelsData) || [];
    } catch (e) {
        console.error("Error getting completed levels:", e);
        return [];
    }
}

// Check if a specific level is completed
function isLevelCompleted(level) {
    const completedLevels = getCompletedLevels();
    return completedLevels.includes(level);
}

// Check if a level is unlocked (either completed or previous level is completed)
function isLevelUnlocked(level) {
    if (level === 1) return true; // First level is always unlocked
    return isLevelCompleted(level - 1);
}

// End the game (player ran out of energy)
function gameOver() {
    if (!gameRunning) return; // Prevent multiple calls
    if (DEBUG_MODE) console.log("gameOver() called - Player ran out of energy or critical error.");

    gameRunning = false;

    // Save high score
    saveHighScore();

    // stopHeartbeat(); // REMOVED - No continuous heartbeat to stop

    // Update UI
    if (uiElements.finalScore) uiElements.finalScore.textContent = score; // Check if element exists

    // Update high score display
    const highScoreElement = document.getElementById('highScore');
    if (highScoreElement) {
        highScoreElement.textContent = getHighScore();
    }

    // Update the game over level display with current level name and segment
    const gameOverLevelElement = document.getElementById('gameOverLevel');
    if (gameOverLevelElement) {
        const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];
        gameOverLevelElement.textContent = `${gameLevel}: ${currentSegment.segment} (avg ${currentSegment.diameter}mm)`;
    }

    // Update restart button based on level completion status
    const restartBtn = document.getElementById('restartBtn');
    const nextLevelBtn = document.getElementById('nextLevelBtn');

    if (restartBtn) {
        // If current level is not completed, show "Retry" text
        if (!isLevelCompleted(gameLevel)) {
            restartBtn.textContent = `Retry Level ${gameLevel}`;
        } else {
            // If completed, show "Replay" text
            restartBtn.textContent = `Replay Level ${gameLevel}`;
        }
    }

    // Update next level button
    if (nextLevelBtn) {
        const nextLevel = (gameLevel % 18) + 1; // Wrap around after level 18
        const nextSegment = coronarySegments.find(segment => segment.level === nextLevel) || coronarySegments[0];

        nextLevelBtn.textContent = `Next: ${nextLevel}: ${nextSegment.segment}`;

        // Disable next level button if current level is not completed
        if (!isLevelCompleted(gameLevel)) {
            nextLevelBtn.disabled = true;
        } else {
            nextLevelBtn.disabled = false;
        }
    }

    // Hide HUD
    const hudElement = document.getElementById('hud');
    if (hudElement) {
        hudElement.style.display = 'none';
    }

    showOverlay(uiElements.gameOverScreen);

    // Stop game intervals
    clearInterval(eggSpawnInterval);
    eggSpawnInterval = null;
    clearInterval(obstacleSpawnInterval);
    obstacleSpawnInterval = null;
    clearInterval(wallCubeSpawnInterval);
    wallCubeSpawnInterval = null;
    clearInterval(redBloodCellSpawnInterval);
    redBloodCellSpawnInterval = null;
    clearInterval(speedIncreaseInterval);
    speedIncreaseInterval = null;
    clearInterval(ammoRegenInterval);
    ammoRegenInterval = null;
    if (DEBUG_MODE) console.log("Game intervals stopped for game over.");

    // Optional: Clear dynamic objects on game over screen? Or leave them for backdrop?
    // clearArray(eggs); clearArray(obstacles); clearArray(lasers); clearArray(wallCubes);
}

// Only log if DEBUG_MODE is explicitly true
if (DEBUG_MODE) {
    console.log("gameLogic.js: GameLogic functions loaded (DEBUG MODE).");
}

// Make functions available globally for non-module scripts
window.startGame = startGame;
window.completeLevel = completeLevel;
window.gameOver = gameOver;
window.findSectionAtPosition = findSectionAtPosition;
window.setGameLevel = setGameLevel;

// System progresywnej trudności
function createProgressiveDifficulty() {
    const baseConfig = {
        easyLevels: [1, 2, 3, 4, 5, 6],
        mediumLevels: [7, 8, 9, 10, 11, 12],
        hardLevels: [13, 14, 15, 16, 17, 18]
    };
    function getCurrentDifficultySettings() {
        const level = gameLevel;
        if (baseConfig.easyLevels.includes(level)) {
            return {
                energyModifier: 0.4,
                spawnRateModifier: 0.8,
                speedModifier: 0.9,
                plaqueCount: 2
            };
        } else if (baseConfig.mediumLevels.includes(level)) {
            return {
                energyModifier: 0.7,
                spawnRateModifier: 1.0,
                speedModifier: 1.0,
                plaqueCount: 4
            };
        } else {
            return {
                energyModifier: 1.0,
                spawnRateModifier: 1.2,
                speedModifier: 1.1,
                plaqueCount: 6
            };
        }
    }
    return getCurrentDifficultySettings();
}

// ===== FUNKCJE POMOCNICZE DLA ZOPTYMALIZOWANEGO SYSTEMU KRWINEK =====

function fallbackToLegacyBloodSystem() {
    console.log("Using legacy blood cell system...");

    // Krwinki czerwone
    setTimeout(() => {
        if (typeof createOptimizedBloodCells === 'function') {
            try {
                createOptimizedBloodCells();
                console.log("Legacy red blood cells created");
            } catch (error) {
                console.error("Error creating legacy red blood cells:", error);
                if (typeof autoSpawnRedBloodCells === 'function') {
                    autoSpawnRedBloodCells();
                }
            }
        }
    }, 500);

    // Białe krwinki - ograniczone do 3 sztuk
    setTimeout(() => {
        if (typeof createLimitedWhiteBloodCells === 'function') {
            createLimitedWhiteBloodCells(3); // Maksymalnie 3 białe krwinki
        } else if (typeof createWhiteBloodCells === 'function') {
            // Modyfikuj konfigurację tymczasowo
            const originalCount = WHITE_BLOOD_CELL_CONFIG.COUNT;
            WHITE_BLOOD_CELL_CONFIG.COUNT = 3;
            createWhiteBloodCells();
            WHITE_BLOOD_CELL_CONFIG.COUNT = originalCount;
        }
    }, 1500);

    // Żółte leukocyty - ograniczone do 2 sztuk
    setTimeout(() => {
        if (typeof createLimitedYellowLeukocytes === 'function') {
            createLimitedYellowLeukocytes(2); // Maksymalnie 2 żółte leukocyty
        } else if (typeof createYellowLeukocytes === 'function') {
            const originalCount = YELLOW_LEUKOCYTE_CONFIG.COUNT;
            YELLOW_LEUKOCYTE_CONFIG.COUNT = 2;
            createYellowLeukocytes();
            YELLOW_LEUKOCYTE_CONFIG.COUNT = originalCount;
        }
    }, 2000);
}

function createLimitedWhiteBloodCells(maxCount = 3) {
    const currentScene = scene || window.scene;
    if (!currentScene || !bunnyCollider || !tunnelSections || tunnelSections.length === 0) {
        return;
    }

    // Wyczyść istniejące białe krwinki
    if (Array.isArray(whiteBloodCells)) {
        whiteBloodCells.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        whiteBloodCells.length = 0;
    }

    // Znajdź sekcję gracza
    const playerSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!playerSection) return;

    // Twórz tylko w pobliżu gracza
    const SPAWN_RANGE = 5;
    const minIndex = Math.max(0, playerSection.index - SPAWN_RANGE);
    const maxIndex = Math.min(tunnelSections.length - 1, playerSection.index + SPAWN_RANGE);

    for (let i = 0; i < maxCount; i++) {
        const sectionIndex = minIndex + Math.floor(Math.random() * (maxIndex - minIndex + 1));
        const section = tunnelSections[sectionIndex];

        if (!section || !section.centerPoint) continue;

        // Utwórz białą krwinkę w sekcji
        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * (0.4 + Math.random() * 0.3);
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;

        const position = new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            section.centerPoint.z + zOffset
        );

        const diameter = WHITE_BLOOD_CELL_CONFIG.MIN_RADIUS + Math.random() *
                        (WHITE_BLOOD_CELL_CONFIG.MAX_RADIUS - WHITE_BLOOD_CELL_CONFIG.MIN_RADIUS);

        const wbcMesh = BABYLON.MeshBuilder.CreateSphere(`limitedWhiteBloodCell_${i}`, {
            diameter: diameter * 2
        }, scene);

        wbcMesh.position = position;

        const wbcMaterial = new BABYLON.StandardMaterial(`limitedWbcMat_${i}`, scene);
        wbcMaterial.diffuseColor = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.COLOR);
        wbcMaterial.emissiveColor = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR).scale(0.5);
        wbcMesh.material = wbcMaterial;

        const wbcLight = new BABYLON.PointLight(`limitedWbcLight_${i}`, position, scene);
        wbcLight.diffuse = BABYLON.Color3.FromHexString(WHITE_BLOOD_CELL_CONFIG.GLOW_COLOR);
        wbcLight.intensity = 1.0;
        wbcLight.range = 5;

        whiteBloodCells.push({
            mesh: wbcMesh,
            light: wbcLight,
            health: WHITE_BLOOD_CELL_CONFIG.HEALTH,
            attackTimer: 0,
            moveDirection: new BABYLON.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize(),
            created: Date.now(),
            limited: true // Oznacz jako ograniczone
        });
    }

    console.log(`Created ${maxCount} limited white blood cells near player`);
}

function createLimitedYellowLeukocytes(maxCount = 2) {
    if (!scene || !bunnyCollider || !tunnelSections || tunnelSections.length === 0) {
        return;
    }

    // Wyczyść istniejące żółte leukocyty
    if (Array.isArray(yellowLeukocytes)) {
        yellowLeukocytes.forEach(cell => {
            if (cell.mesh) cell.mesh.dispose();
            if (cell.light) cell.light.dispose();
        });
        yellowLeukocytes.length = 0;
    }

    // Znajdź sekcję gracza
    const playerSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!playerSection) return;

    // Twórz tylko przed graczem (bardziej agresywne)
    const SPAWN_RANGE = 3;
    const minIndex = Math.max(0, playerSection.index - SPAWN_RANGE);
    const maxIndex = Math.min(tunnelSections.length - 1, playerSection.index + 1);

    for (let i = 0; i < maxCount; i++) {
        const sectionIndex = minIndex + Math.floor(Math.random() * (maxIndex - minIndex + 1));
        const section = tunnelSections[sectionIndex];

        if (!section || !section.centerPoint) continue;

        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * (0.3 + Math.random() * 0.3);
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;

        const position = new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            section.centerPoint.z + zOffset
        );

        const diameter = YELLOW_LEUKOCYTE_CONFIG.MIN_RADIUS + Math.random() *
                        (YELLOW_LEUKOCYTE_CONFIG.MAX_RADIUS - YELLOW_LEUKOCYTE_CONFIG.MIN_RADIUS);

        const ylMesh = BABYLON.MeshBuilder.CreateSphere(`limitedYellowLeukocyte_${i}`, {
            diameter: diameter * 2
        }, scene);

        ylMesh.position = position;

        const ylMaterial = new BABYLON.StandardMaterial(`limitedYlMat_${i}`, scene);
        ylMaterial.diffuseColor = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.COLOR);
        ylMaterial.emissiveColor = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR).scale(0.6);
        ylMesh.material = ylMaterial;

        const ylLight = new BABYLON.PointLight(`limitedYlLight_${i}`, position, scene);
        ylLight.diffuse = BABYLON.Color3.FromHexString(YELLOW_LEUKOCYTE_CONFIG.GLOW_COLOR);
        ylLight.intensity = 1.2;
        ylLight.range = 6;

        yellowLeukocytes.push({
            mesh: ylMesh,
            light: ylLight,
            health: YELLOW_LEUKOCYTE_CONFIG.HEALTH,
            attackTimer: 0,
            moveDirection: new BABYLON.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ).normalize(),
            homingTarget: null,
            created: Date.now(),
            limited: true // Oznacz jako ograniczone
        });
    }

    console.log(`Created ${maxCount} limited yellow leukocytes near player`);
}

function checkOptimizedCollisions() {
    if (!bunnyCollider || !scene || !gameRunning) return;

    // Sprawdź kolizje tylko dla obiektów zarządzanych przez BiologicalManager
    if (biologicalManager) {
        // Sprawdź kolizje z białymi krwinkami
        if (biologicalManager.activeWhiteCells.length > 0) {
            window.whiteBloodCells = biologicalManager.activeWhiteCells;
            if (typeof checkLaserWhiteBloodCellCollisions === 'function' &&
                Array.isArray(lasers) && lasers.length > 0) {
                checkLaserWhiteBloodCellCollisions();
            }
        }

        // Sprawdź kolizje z żółtymi leukocytami
        if (biologicalManager.activeYellowCells.length > 0) {
            window.yellowLeukocytes = biologicalManager.activeYellowCells;
            if (typeof checkLaserYellowLeukocyteCollisions === 'function' &&
                Array.isArray(lasers) && lasers.length > 0) {
                checkLaserYellowLeukocyteCollisions();
            }
        }
    }
}

// Eksport funkcji do globalnego scope
window.fallbackToLegacyBloodSystem = fallbackToLegacyBloodSystem;
window.createLimitedWhiteBloodCells = createLimitedWhiteBloodCells;
window.createLimitedYellowLeukocytes = createLimitedYellowLeukocytes;
window.checkOptimizedCollisions = checkOptimizedCollisions;

// --- END OF FILE gameLogic.js ---
