// --- START OF FILE ui.js --- // Assuming this file exists
// Helper function to call startGame
async function callStartGame() {
    if (typeof startGame === 'function') {
        return startGame();
    } else {
        console.error("startGame function is not defined!");
        throw new Error("Game start function not available");
    }
}

/**
 * Sets up the multiplayer settings UI elements and handlers
 */
function setupMultiplayerSettings() {
    getUIElementReferences();
    const allowJoiningCheckbox = document.getElementById('allowJoining');
    const playerUsernameInput = document.getElementById('playerUsername');
    const colorOptions = document.querySelectorAll('.color-option');
    if (allowJoiningCheckbox) {
        const allowJoiningValue = localStorage.getItem('allowJoining') !== 'false';
        allowJoiningCheckbox.checked = allowJoiningValue;
        allowJoining = allowJoiningValue;
    }
    if (playerUsernameInput) {
        const usernameValue = localStorage.getItem('playerUsername') || '';
        playerUsernameInput.value = usernameValue;
        playerUsername = usernameValue;
    }
    if (colorOptions && colorOptions.length > 0) {
        const savedColorName = localStorage.getItem('playerColor') || 'blue';
        colorOptions.forEach(option => {
            option.classList.remove('selected');
            option.addEventListener('click', () => {
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });
        const savedOption = Array.from(colorOptions).find(opt =>
            opt.getAttribute('data-color') === savedColorName
        );
        if (savedOption) {
            savedOption.classList.add('selected');
        } else if (colorOptions[0]) {
            colorOptions[0].classList.add('selected');
        }
    }
    updateConnectionStatus();
}

/**
 * Updates the connection status display in the multiplayer settings
 */
function updateConnectionStatus() {
    try {
        // Ensure UI elements are initialized before updating connection status
        if (typeof getUIElementReferences === 'function') {
            getUIElementReferences();
        }

        const connectionStatus = document.getElementById('connectionStatus');
        const onlineCount = document.getElementById('onlineCount');

        if (!connectionStatus) return;

        // Check if wpsclient is defined and connected
        if (typeof wpsclient !== 'undefined' && wpsclient !== null) {
            // Remove all status classes
            connectionStatus.classList.remove('connected', 'connecting', 'disconnected');

            // Check client state and update status
            if (wpsclient.connected) {
                connectionStatus.textContent = 'Connected to multiplayer server';
                connectionStatus.classList.add('connected');
            } else if (wpsclient.connecting) {
                connectionStatus.textContent = 'Connecting to multiplayer server...';
                connectionStatus.classList.add('connecting');
            } else {
                connectionStatus.textContent = 'Disconnected from multiplayer server';
                connectionStatus.classList.add('disconnected');
            }

            // Update player count if available
            if (onlineCount && typeof getConnectedPlayerCount === 'function') {
                onlineCount.textContent = getConnectedPlayerCount();
            }
        } else {
            connectionStatus.textContent = 'Multiplayer service not available - offline mode';
            connectionStatus.classList.add('disconnected');

            if (onlineCount) {
                onlineCount.textContent = '0';
            }
        }
    } catch (error) {
        console.warn("Error updating connection status:", error);
    }
}

/**
 * Saves multiplayer settings from the UI to localStorage and globals
 */
function saveMultiplayerSettings() {
    // Ensure UI elements are initialized before saving multiplayer settings
    getUIElementReferences();

    const allowJoiningCheckbox = document.getElementById('allowJoining');
    const playerUsernameInput = document.getElementById('playerUsername');
    const selectedColorOption = document.querySelector('.color-option.selected');

    // Save Allow Joining setting
    if (allowJoiningCheckbox) {
        const allowJoiningValue = allowJoiningCheckbox.checked;
        localStorage.setItem('allowJoining', allowJoiningValue);

        // Update global if setter exists
        allowJoining = allowJoiningValue;


        // Make available to window for multiplayer module
        window.allowMultiplayerJoining = allowJoiningValue;
    }

    // Save Player Username
    if (playerUsernameInput) {
        const usernameValue = playerUsernameInput.value.trim();
        localStorage.setItem('playerUsername', usernameValue);

        // Update global if setter exists
        playerUsername = usernameValue;
    }

    // Save Player Color
    if (selectedColorOption) {
        const colorName = selectedColorOption.getAttribute('data-color');
        if (colorName) {
            localStorage.setItem('playerColor', colorName);

            // If in a multiplayer session, send color update
            if (typeof wpsclient !== 'undefined' && wpsclient !== null && typeof playerId !== 'undefined') {
                try {
                    if (wpsclient.connected) {
                        wpsclient.sendToGroup("lobby", {
                            type: "playerColorChanged",
                            playerId: playerId,
                            playerName: playerUsernameInput ? playerUsernameInput.value : 'Player',
                            color: colorName
                        }, "text").catch(err => console.error("Error sending color update:", err));
                    } else {
                        console.log("Not sending color update - client not connected");
                    }
                } catch (e) {
                    console.warn("Could not send color update to other players:", e);
                }
            }
        }
    }

    if (DEBUG_MODE) {
        console.log("Multiplayer settings saved:", {
            allowJoining: allowJoiningCheckbox ? allowJoiningCheckbox.checked : 'N/A',
            username: playerUsernameInput ? playerUsernameInput.value : 'N/A',
            color: selectedColorOption ? selectedColorOption.getAttribute('data-color') : 'N/A'
        });
    }
}

/**
 * Updates the Heads-Up Display (HUD) elements with current game state.
 */
function updateHUD() {
    // Check if waiting for camera
    // Note: We don't create a waiting message here anymore
    // The waiting message is now handled in gameLoop function in gameLogic.js
    // This ensures we don't have duplicate messages for scene initialization and camera reaching player

    // Update Score
    if (uiElements.scoreDisplay) {
        uiElements.scoreDisplay.textContent = score;
    }

    // Update Energy (value and visual bar)
    if (uiElements.energyDisplay) {
        uiElements.energyDisplay.textContent = Math.max(0, Math.floor(energy)); // Show non-negative integer
    }
    const energyBar = document.getElementById('energyBarFill'); // Get bar element
    if (energyBar) {
        const energyPercent = Math.max(0, Math.min(100, energy)); // Clamp 0-100
        energyBar.style.width = energyPercent + '%'; // Set width percentage
        // Change color based on energy level
        if (energyPercent < 25) {
            energyBar.style.backgroundColor = '#ff4d4d';
        } // Red
        else if (energyPercent < 50) {
            energyBar.style.backgroundColor = '#ffa64d';
        } // Orange
        else {
            energyBar.style.backgroundColor = '#99e64d';
        } // Greenish-yellow
    }

    // Update Speed Multiplier
    if (uiElements.speedDisplay) {
        uiElements.speedDisplay.textContent = currentSpeedMultiplier.toFixed(1) + 'x'; // Format to one decimal place
    }

    // Update Ammo Count
    if (uiElements.ammoDisplay) {
        uiElements.ammoDisplay.textContent = ammo;
    }

    // Update High Score
    const hudHighScoreElement = document.getElementById('hudHighScore');
    if (hudHighScoreElement) {
        hudHighScoreElement.textContent = typeof getHighScore === 'function' ? getHighScore() : 0;
    }

    // Update Level Display with more detailed information
    if (uiElements.levelDisplay) { // Check if the element exists
        try {
            // Find the current coronary segment based on gameLevel
            const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];

            // Create a more detailed display with level number, segment name, and diameter
            // Format: "Level X: Segment Name (avg Xmm) - Description"
            let levelInfo = `${gameLevel}: ${currentSegment.segment} (avg ${currentSegment.diameter}mm)`;

            // Add a tooltip with more detailed description
            if (currentSegment.description) {
                uiElements.levelDisplay.title = currentSegment.description;
            }

            // Update the display
            uiElements.levelDisplay.textContent = levelInfo;

            // Debug log to check if level display is being updated
            if (DEBUG_MODE) console.log(`updateHUD: Level display updated to ${levelInfo}`);
        } catch (error) {
            console.error("Error updating level display:", error);
            // Fallback to basic display
            uiElements.levelDisplay.textContent = `Level ${gameLevel}`;
        }
    } else {
        if (DEBUG_MODE) console.warn("updateHUD: levelDisplay element not found!");
    }

    // Update Diagnostics (if in DEBUG mode)
    if (DEBUG_MODE && uiElements.diagnostics) {
        const playerPos = bunnyCollider ? bunnyCollider.position : null;
// Format diagnostics information clearly
        uiElements.diagnostics.innerHTML = `
             Pos Z: ${playerPos ? playerPos.z.toFixed(1) : 'N/A'} |
             Section: ${currentSectionIndex} |
             Speed: ${currentSpeedMultiplier.toFixed(2)}x |
             Energy: ${energy.toFixed(1)} | <br>
             Objs (E/O/L/WC): ${eggs.length}/${obstacles.length}/${lasers.length}/${wallCubes.length} |
             FPS: ${engine ? engine.getFps().toFixed(0) : 'N/A'}
         `;
    } else if (uiElements.diagnostics) {
        uiElements.diagnostics.innerHTML = ''; // Clear diagnostics if not debugging
    }
}

// Function to update the minimap display
function updateMinimap() {
    if (!uiElements.minimap || !bunnyCollider || !tunnelSections || tunnelSections.length === 0) {
        if (uiElements.minimap) uiElements.minimap.innerHTML = ''; // Clear minimap if data is missing
        return;
    }

    const ctx = uiElements.minimap.getContext('2d');
    const width = uiElements.minimap.width;
    const height = uiElements.minimap.height;
    const lookAhead = 15; // Sections ahead
    const lookBehind = 5;  // Sections behind

    ctx.clearRect(0, 0, width, height); // Clear previous frame

    // Calculate visible section range
    const startIdx = Math.max(0, currentSectionIndex - lookBehind);
    const endIdx = Math.min(tunnelSections.length - 1, currentSectionIndex + lookAhead);

    if (startIdx >= endIdx) return; // Not enough sections

    // Find min/max X for scaling
    let minX = Infinity, maxX = -Infinity;
    for (let i = startIdx; i <= endIdx; i++) {
        // Ensure radius exists, use 0 if not (shouldn't happen with checks)
        const radius = tunnelSections[i].radius || 0;
        minX = Math.min(minX, tunnelSections[i].centerPoint.x - radius);
        maxX = Math.max(maxX, tunnelSections[i].centerPoint.x + radius);
    }
    const tunnelWidthWorld = Math.max(1, maxX - minX);

    // Define scaling factors and offsets
    const xScale = width / tunnelWidthWorld * 0.9; // Use 90% width// Center view

    // *** FIX: Declare yPlayer and zScale BEFORE the loop ***
    const yPlayer = height * 0.8; // Player's fixed Y position on map (e.g., 80% down)
    // Calculate Z scaling based on the Z range of visible sections
    const zRange = Math.abs(tunnelSections[endIdx].centerPoint.z - tunnelSections[startIdx].centerPoint.z);
    const zScale = zRange > 1 ? (height * 0.8) / zRange : 1; // Map Z range to 80% of map height

    // Calculate xOffset for mapping world X to minimap X
    const xOffset = width / 2 - (minX + tunnelWidthWorld / 2) * xScale; // Center the view

    // --- Draw background gradient ---
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, 'rgba(20, 20, 50, 0.7)');
    gradient.addColorStop(1, 'rgba(60, 10, 10, 0.7)');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // --- Draw Tunnel Sections (Top-down view) ---
    for (let i = startIdx; i < endIdx; i++) { // Iterate up to endIdx-1 to draw segments
        const current = tunnelSections[i];
        const next = tunnelSections[i + 1];

        // Map world Z to minimap Y (invert Z -> Y, scale to fit height)
        // Player is roughly at the bottom 1/5th of the map
        const mapYCurrent = yPlayer - (current.centerPoint.z - bunnyCollider.position.z) * zScale;
        const mapYNext = yPlayer - (next.centerPoint.z - bunnyCollider.position.z) * zScale;

        // Map world X to minimap X (center and scale)
        const xOffset = width / 2 - (minX + tunnelWidthWorld / 2) * xScale; // Center the view

        const mapXLeftCurrent = (current.centerPoint.x - current.radius) * xScale + xOffset;
        const mapXRightCurrent = (current.centerPoint.x + current.radius) * xScale + xOffset;
        const mapXLeftNext = (next.centerPoint.x - next.radius) * xScale + xOffset;
        const mapXRightNext = (next.centerPoint.x + next.radius) * xScale + xOffset;

        // Calculate distance from player for color gradient
        const distanceFromPlayer = Math.abs(current.centerPoint.z - bunnyCollider.position.z);
        const normalizedDistance = Math.min(1, distanceFromPlayer / (lookAhead * 5));

        // Create color based on distance (closer = more red, farther = more blue)
        const red = Math.floor(200 - normalizedDistance * 150);
        const green = Math.floor(100 + normalizedDistance * 50);
        const blue = Math.floor(100 + normalizedDistance * 150);
        const alpha = Math.max(0.3, 1 - normalizedDistance * 0.7);

        // Set line style based on distance
        ctx.strokeStyle = `rgba(${red}, ${green}, ${blue}, ${alpha})`;
        ctx.lineWidth = Math.max(1, 3 - normalizedDistance * 2);

        // Draw tunnel walls with fill
        ctx.beginPath();
        ctx.moveTo(mapXLeftCurrent, mapYCurrent);
        ctx.lineTo(mapXLeftNext, mapYNext);
        ctx.lineTo(mapXRightNext, mapYNext);
        ctx.lineTo(mapXRightCurrent, mapYCurrent);
        ctx.closePath();

        // Fill with semi-transparent gradient
        const sectionGradient = ctx.createLinearGradient(0, mapYCurrent, 0, mapYNext);
        sectionGradient.addColorStop(0, `rgba(${red}, ${green}, ${blue}, 0.1)`);
        sectionGradient.addColorStop(1, `rgba(${red}, ${green}, ${blue}, 0.2)`);
        ctx.fillStyle = sectionGradient;
        ctx.fill();

        // Stroke the outline
        ctx.stroke();

        // Draw narrow points marker with glow effect
        if (current.narrowing) {
            // Draw glow
            const glowRadius = 6;
            const gradient = ctx.createRadialGradient(
                (mapXLeftCurrent + mapXRightCurrent) / 2, mapYCurrent, 1,
                (mapXLeftCurrent + mapXRightCurrent) / 2, mapYCurrent, glowRadius
            );
            gradient.addColorStop(0, 'rgba(255, 50, 50, 0.8)');
            gradient.addColorStop(1, 'rgba(255, 50, 50, 0)');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc((mapXLeftCurrent + mapXRightCurrent) / 2, mapYCurrent, glowRadius, 0, Math.PI * 2);
            ctx.fill();

            // Draw center point
            ctx.fillStyle = 'rgba(255, 50, 50, 0.9)';
            ctx.beginPath();
            ctx.arc((mapXLeftCurrent + mapXRightCurrent) / 2, mapYCurrent, 2, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    // --- Draw Player with glow effect ---
    const playerMapX = width / 2; // Player always centered horizontally on map
    const playerMapY = yPlayer; // Player fixed vertically

    // Draw player glow
    const playerGlowRadius = 8;
    const playerGlow = ctx.createRadialGradient(
        playerMapX, playerMapY, 1,
        playerMapX, playerMapY, playerGlowRadius
    );
    playerGlow.addColorStop(0, 'rgba(255, 255, 100, 0.8)');
    playerGlow.addColorStop(1, 'rgba(255, 255, 0, 0)');

    ctx.fillStyle = playerGlow;
    ctx.beginPath();
    ctx.arc(playerMapX, playerMapY, playerGlowRadius, 0, Math.PI * 2);
    ctx.fill();

    // Draw player
    ctx.fillStyle = 'rgba(255, 255, 0, 0.9)';
    ctx.beginPath();
    ctx.arc(playerMapX, playerMapY, 3, 0, Math.PI * 2);
    ctx.fill();

    // Add direction indicator
    ctx.strokeStyle = 'rgba(255, 255, 0, 0.7)';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(playerMapX, playerMapY);
    ctx.lineTo(playerMapX, playerMapY - 8);
    ctx.stroke();

    // --- Draw Objects (simplified: just count for now) ---
    // Drawing individual objects accurately can be complex and slow
    // ctx.fillStyle = 'lime'; // Eggs
    // ctx.fillText(`E:${eggs.length}`, 5, height - 25);
    // ctx.fillStyle = 'red'; // Obstacles
    // ctx.fillText(`O:${obstacles.length}`, 5, height - 15);
    // ctx.fillStyle = 'cyan'; // Lasers
    // ctx.fillText(`L:${lasers.length}`, 5, height - 5);

    // --- Draw Average Diameter Information ---
    // Find the current coronary segment based on gameLevel
    const currentSegment = coronarySegments.find(segment => segment.level === gameLevel) || coronarySegments[0];

    // Set text properties
    ctx.font = '10px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'bottom';

    // Draw the average diameter text in the bottom right corner
    ctx.fillText(`Avg Ø: ${currentSegment.diameter}mm`, width - 5, height - 5);

    // --- Draw Obstacle Type Information ---
    // Check if there are any obstacles in view
    if (obstacles.length > 0) {
        // Draw a small legend in the top left corner
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.font = '8px Arial';

        // Find the nearest obstacle to the player
        let nearestObstacle = null;
        let minDistance = Infinity;

        for (const obstacle of obstacles) {
            if (obstacle.mesh && !obstacle.mesh.isDisposed()) {
                const distance = BABYLON.Vector3.Distance(bunnyCollider.position, obstacle.mesh.position);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestObstacle = obstacle;
                }
            }
        }

        // If we found a nearby obstacle, show its type
        if (nearestObstacle && minDistance < 20) { // Only show if within 20 units
            const lesionType = nearestObstacle.lesionType;
            const isDestructible = nearestObstacle.isDestructible;

            // Find the atherosclerotic type data
            const typeData = atheroscleroticTypes.find(t => t.type === lesionType);

            if (typeData) {
                // Draw a colored box for the obstacle type
                ctx.fillStyle = typeData.color;
                ctx.fillRect(5, 5, 10, 10);

                // Draw the obstacle type name
                ctx.fillStyle = 'white';
                ctx.fillText(`Nearby: Type ${lesionType}: ${typeData.englishName}`, 20, 5);
                ctx.fillText(`${isDestructible ? 'Destructible' : 'Indestructible'}`, 20, 15);
            }
        }
    }

    // --- Draw Red Blood Cells ---
    if (window.rbcData && window.rbcData.length > 0) {
        // Set style for red blood cells
        ctx.fillStyle = '#d22'; // Bright red color

        // Draw each red blood cell as a small dot
        for (const cell of window.rbcData) {
            if (!cell || !cell.position) continue;

            // Calculate distance from player
            const distanceFromPlayer = Math.abs(cell.position.z - bunnyCollider.position.z);

            // Only draw cells within visible range
            if (distanceFromPlayer > (lookAhead + lookBehind) * 5) continue;

            // Map world coordinates to minimap coordinates
            const mapX = cell.position.x * xScale + xOffset;
            const mapY = yPlayer - (cell.position.z - bunnyCollider.position.z) * zScale;

            // Check if the cell is within the minimap bounds
            if (mapX < 0 || mapX > width || mapY < 0 || mapY > height) continue;

            // Draw the cell as a small circle
            const dotSize = 2; // Size of the dot
            ctx.beginPath();
            ctx.arc(mapX, mapY, dotSize, 0, Math.PI * 2);
            ctx.fill();
        }
    }
}

// Get references to UI elements from the DOM
// Globalna flaga do sprawdzania czy UI jest zainicjalizowane
let uiInitialized = false;

/**
 * Bezpiecznie pobiera element DOM, tworząc fallback jeśli nie istnieje
 * @param {string} elementId - ID elementu do pobrania
 * @param {boolean} createFallback - Czy tworzyć element zastępczy jeśli nie istnieje
 * @returns {HTMLElement|null} - Znaleziony lub utworzony element, lub null
 */
function safeGetElement(elementId, createFallback = true) {
    let element = document.getElementById(elementId);

    if (!element && createFallback) {
        console.warn(`Creating fallback element for missing: ${elementId}`);
        element = document.createElement('div');
        element.id = elementId;
        element.style.display = 'none';
        document.body.appendChild(element);
    }

    return element;
}

// Ulepszona funkcja getUIElementReferences
function getUIElementReferences() {
    console.log("=== INITIALIZING UI ELEMENTS ===");

    if (uiInitialized) {
        console.log("UI already initialized, skipping...");
        return;
    }

    // Sprawdź czy DOM jest załadowany
    if (document.readyState !== 'complete') {
        console.log("DOM not ready, waiting...");
        window.addEventListener('load', getUIElementReferences, {once: true});
        return;
    }

    // ===================
    // SCREEN ELEMENTS
    // ===================
    uiElements.loadingScreen = document.getElementById("loadingScreen");
    uiElements.startScreen = document.getElementById("startScreen");
    uiElements.gameOverScreen = document.getElementById("gameOverScreen");
    uiElements.levelListScreen = document.getElementById("levelListScreen");
    uiElements.settingsScreen = document.getElementById("settingsScreen");
    uiElements.infoScreen = document.getElementById("infoScreen");

    // Dodaj bezpieczne pobieranie elementów startWindow i startWindowLogo
    uiElements.startWindow = safeGetElement("startWindow");
    uiElements.startWindowLogo = safeGetElement("startWindowLogo");

    // ===================
    // HUD ELEMENTS - KRITYCZNE!
    // ===================
    uiElements.scoreDisplay = document.getElementById("score");
    uiElements.energyDisplay = document.getElementById("energy");
    uiElements.speedDisplay = document.getElementById("speed");
    uiElements.ammoDisplay = document.getElementById("ammo");

    // LEVEL DISPLAY - sprawdź różne możliwości
    uiElements.levelDisplay = document.getElementById("level");
    if (!uiElements.levelDisplay) {
        console.warn("Level display not found by ID, creating...");
        // Utwórz element level jeśli nie istnieje
        const levelElement = document.createElement("span");
        levelElement.id = "level";
        levelElement.textContent = "1";

        // Znajdź HUD i dodaj
        const hud = document.getElementById("hud");
        if (hud) {
            const levelContainer = document.createElement("div");
            levelContainer.innerHTML = "Level: ";
            levelContainer.appendChild(levelElement);
            hud.insertBefore(levelContainer, hud.firstChild);
            uiElements.levelDisplay = levelElement;
            console.log("Level display created and added to HUD");
        }
    }

    // ===================
    // BUTTON ELEMENTS
    // ===================
    uiElements.startGameBtn = document.getElementById("startGameBtn");
    uiElements.settingsBtn = document.getElementById("settingsBtn");
    uiElements.infoBtn = document.getElementById("infoBtn");
    uiElements.levelSelectBtn = document.getElementById("levelSelectBtn");
    uiElements.restartBtn = document.getElementById("restartBtn");
    uiElements.nextLevelBtn = document.getElementById("nextLevelBtn");
    uiElements.backToStartBtn = document.getElementById("backToStartBtn");
    uiElements.backFromSettingsBtn = document.getElementById("backFromSettingsBtn");
    uiElements.backFromInfoBtn = document.getElementById("backFromInfoBtn");
    uiElements.saveSettingsBtn = document.getElementById("saveSettingsBtn");
    uiElements.soundControl = document.getElementById("soundControl");

    // ===================
    // OTHER ELEMENTS
    // ===================
    uiElements.minimap = document.getElementById("minimap");
    uiElements.diagnostics = document.getElementById("diagnostics");
    uiElements.finalScore = document.getElementById("finalScore");
    uiElements.gameInfoContent = document.getElementById("gameInfoContent");
    uiElements.levelListContainer = document.getElementById("levelList");

    // ===================
    // VALIDATION
    // ===================
    const criticalElements = {
        startScreen: uiElements.startScreen,
        levelDisplay: uiElements.levelDisplay,
        scoreDisplay: uiElements.scoreDisplay,
        energyDisplay: uiElements.energyDisplay,
        startGameBtn: uiElements.startGameBtn
    };

    const missingCritical = Object.entries(criticalElements)
        .filter(([name, element]) => !element)
        .map(([name]) => name);

    if (missingCritical.length > 0) {
        console.error("CRITICAL UI ELEMENTS MISSING:", missingCritical);
    } else {
        console.log("All critical UI elements found");
        uiInitialized = true;
    }

    console.log("=== UI ELEMENTS INITIALIZATION COMPLETE ===");

    // Create or get settings-related buttons
    ensureButtonExists("settingsBtn", "Settings", "startScreen");
    ensureButtonExists("infoBtn", "Information", "startScreen");
    ensureButtonExists("levelSelectBtn", "Level Select", "startScreen");
    ensureButtonExists("joinGameBtn", "Join Game", "startScreen");
    ensureButtonExists("soundControl", "🔊", "startScreen");

    // Multiplayer is now automatically started with the game

    // Get references to the buttons
    uiElements.settingsWindowBtn = document.getElementById("settingsBtn");
    uiElements.infoWindowBtn = document.getElementById("infoBtn");
    uiElements.levelSelectBtn = document.getElementById("levelSelectBtn");
    uiElements.soundControl = document.getElementById("soundControl");
    uiElements.createGameBtn = document.getElementById("createGameBtn");
    uiElements.joinGameBtn = document.getElementById("joinGameBtn");

    // Ensure settings screen buttons exist
    ensureButtonExists("saveSettingsBtn", "Save Settings", "settingsScreen");
    ensureButtonExists("backFromSettingsBtn", "Back", "settingsScreen");

    // Ensure game over screen buttons exist
    ensureButtonExists("restartBtn", "Retry Level", "gameOverScreen");
    ensureButtonExists("nextLevelBtn", "Next Level", "gameOverScreen");
    ensureButtonExists("levelSelectFromGameOverBtn", "Level Select", "gameOverScreen");

    // Ensure back button exists
    ensureButtonExists("backFromInfoBtn", "Back", "infoScreen");
    ensureButtonExists("backToStartBtn", "Back", "levelListScreen");

    // Buttons within Start Screen (Fallback?)
    uiElements.startGameBtn = document.getElementById("startGameBtn");
    uiElements.introMessage = document.getElementById("introMessage");

    // Settings screen elements
    uiElements.saveSettingsBtn = document.getElementById("saveSettingsBtn");
    uiElements.backFromSettingsBtn = document.getElementById("backFromSettingsBtn");

    // Ensure settings checkboxes exist
    ensureCheckboxExists("effectsEnabled", "Enable Visual Effects", "settingsScreen");
    ensureCheckboxExists("musicEnabled", "Enable Background Music", "settingsScreen");
    ensureCheckboxExists("gyroEnabled", "Enable Gyroscope Controls", "settingsScreen");

    // Ensure player username input exists
    ensureInputExists("playerUsername", "Enter your username", "settingsScreen");

    // Add multiplayer game buttons to the settings screen
    const settingsScreen = document.getElementById("settingsScreen");
    if (settingsScreen) {
        // Check if multiplayer buttons container already exists
        let multiplayerButtonsContainer = document.getElementById("multiplayerButtonsContainer");
        if (!multiplayerButtonsContainer) {
            // Create a container for multiplayer buttons
            multiplayerButtonsContainer = document.createElement("div");
            multiplayerButtonsContainer.id = "multiplayerButtonsContainer";
            multiplayerButtonsContainer.className = "multiplayer-buttons-container";

            // Add a heading
            const heading = document.createElement("h3");
            heading.textContent = "Multiplayer Options";
            multiplayerButtonsContainer.appendChild(heading);

            // Add description
            const description = document.createElement("p");
            description.className = "setting-description";
            description.textContent = "Join an existing multiplayer game.";
            multiplayerButtonsContainer.appendChild(description);

            // Add to settings screen
            const settingsContainer = settingsScreen.querySelector(".settings-container") || settingsScreen;
            settingsContainer.appendChild(multiplayerButtonsContainer);
        }

        // Create join button inside the container if it doesn't already exist
        let joinBtn = document.getElementById("joinMultiplayerGameBtn");
        if (!joinBtn) {
            joinBtn = document.createElement("button");
            joinBtn.id = "joinMultiplayerGameBtn";
            joinBtn.textContent = "Join Multiplayer Game";
            multiplayerButtonsContainer.appendChild(joinBtn);
        }
    }

    // Ensure music track radios exist
    ensureMusicTrackRadiosExist();

    uiElements.effectsEnabledCheckbox = document.getElementById("effectsEnabled");
    uiElements.musicEnabledCheckbox = document.getElementById("musicEnabled");
    uiElements.learningModeEnabledCheckbox = document.getElementById("learningModeEnabled");
    uiElements.musicTrackRadios = document.getElementsByName("musicTrack");
    uiElements.allowJoiningCheckbox = document.getElementById("allowJoining");
    uiElements.gyroEnabledCheckbox = document.getElementById("gyroEnabled");
    uiElements.playerUsernameInput = document.getElementById("playerUsername");

    // Info screen elements
    uiElements.gameInfoContent = document.getElementById("gameInfoContent");
    uiElements.backFromInfoBtn = document.getElementById("backFromInfoBtn");

    // Game over screen elements
    uiElements.finalScore = document.getElementById("finalScore");
    uiElements.restartBtn = document.getElementById("restartBtn");
    uiElements.nextLevelBtn = document.getElementById("nextLevelBtn");
    uiElements.levelSelectFromGameOverBtn = document.getElementById("levelSelectFromGameOverBtn");

    // Level list screen elements
    uiElements.levelListContainer = document.getElementById("levelList");
    uiElements.backToStartBtn = document.getElementById("backToStartBtn");

    // Other UI elements
    uiElements.diagnostics = document.getElementById("diagnostics");
    uiElements.minimap = document.getElementById("minimap");

    // Helper function to ensure a button exists in a specific screen
    function ensureButtonExists(id, text, screenId) {
        const button = document.getElementById(id);
        if (!button) {
            console.warn(`${id} not found in DOM, creating it`);
            const screen = document.getElementById(screenId);
            if (screen) {
                const overlayContent = screen.querySelector(".overlay-content");
                if (overlayContent) {
                    const newBtn = document.createElement("button");
                    newBtn.id = id;
                    newBtn.textContent = text;
                    // Add to the overlay content
                    overlayContent.appendChild(newBtn);
                    console.log(`Created missing ${id} element`);
                    return newBtn;
                }
            }
        }
        return button;
    }

    // Helper function to ensure a checkbox exists in a specific screen
    function ensureCheckboxExists(id, label, screenId) {
        const checkbox = document.getElementById(id);
        if (!checkbox) {
            console.warn(`${id} not found in DOM, creating it`);
            const screen = document.getElementById(screenId);
            if (screen) {
                // Find the appropriate container for this checkbox
                let container;
                if (id === "effectsEnabled" || id === "musicEnabled") {
                    // These go in the Audio & Visual section
                    container = screen.querySelector(".setting-group:first-child .setting-option:last-child");
                } else if (id === "allowJoining") {
                    // This goes in the Multiplayer section
                    container = screen.querySelector(".setting-group:nth-child(2) .setting-option:first-child");
                }

                if (!container) {
                    // If we couldn't find a specific container, just use the first setting group
                    container = screen.querySelector(".setting-group");
                    if (!container) {
                        // If there's no setting group, use the overlay content
                        container = screen.querySelector(".overlay-content");
                    }
                }

                if (container) {
                    const newLabel = document.createElement("label");
                    const newCheckbox = document.createElement("input");
                    newCheckbox.type = "checkbox";
                    newCheckbox.id = id;
                    newCheckbox.checked = true; // Default to enabled

                    newLabel.appendChild(newCheckbox);
                    newLabel.appendChild(document.createTextNode(" " + label));

                    // Create a div to wrap the label
                    const newDiv = document.createElement("div");
                    newDiv.className = "setting-option";
                    newDiv.appendChild(newLabel);

                    // Add to the container
                    container.appendChild(newDiv);
                    console.log(`Created missing ${id} element`);
                    return newCheckbox;
                }
            }
        }
        return checkbox;
    }

    // Helper function to ensure an input exists in a specific screen
    function ensureInputExists(id, placeholder, screenId) {
        const input = document.getElementById(id);
        if (!input) {
            console.warn(`${id} not found in DOM, creating it`);
            const screen = document.getElementById(screenId);
            if (screen) {
                // Find the appropriate container for this input
                let container;
                if (id === "playerUsername") {
                    // This goes in the Multiplayer section
                    container = screen.querySelector(".setting-group:nth-child(2) .setting-option:nth-child(2)");
                }

                if (!container) {
                    // If we couldn't find a specific container, just use the first setting group
                    container = screen.querySelector(".setting-group");
                    if (!container) {
                        // If there's no setting group, use the overlay content
                        container = screen.querySelector(".overlay-content");
                    }
                }

                if (container) {
                    const newLabel = document.createElement("label");
                    newLabel.setAttribute("for", id);
                    newLabel.textContent = "Your Username:";

                    const newInput = document.createElement("input");
                    newInput.type = "text";
                    newInput.id = id;
                    newInput.placeholder = placeholder;

                    // Create a div to wrap the label and input
                    const newDiv = document.createElement("div");
                    newDiv.className = "setting-option";
                    newDiv.appendChild(newLabel);
                    newDiv.appendChild(newInput);

                    // Add to the container
                    container.appendChild(newDiv);
                    console.log(`Created missing ${id} element`);
                    return newInput;
                }
            }
        }
        return input;
    }

    // Helper function to ensure music track radios exist
    function ensureMusicTrackRadiosExist() {
        const radios = document.getElementsByName("musicTrack");
        if (!radios || radios.length === 0) {
            console.warn("musicTrack radios not found in DOM, creating them");
            const screen = document.getElementById("settingsScreen");
            if (screen) {
                // Find the appropriate container for the music track radios
                let container = screen.querySelector("#musicSelection");

                if (!container) {
                    // If we couldn't find the music selection container, create it
                    container = document.createElement("div");
                    container.id = "musicSelection";
                    container.className = "setting-option";

                    const heading = document.createElement("h3");
                    heading.textContent = "Background Music Track";
                    container.appendChild(heading);

                    // Find the Audio & Visual section to add it to
                    const audioVisualSection = screen.querySelector(".setting-group:first-child");
                    if (audioVisualSection) {
                        audioVisualSection.appendChild(container);
                    } else {
                        // If there's no Audio & Visual section, use the overlay content
                        const overlayContent = screen.querySelector(".overlay-content");
                        if (overlayContent) {
                            overlayContent.appendChild(container);
                        }
                    }
                }

                if (container) {
                    // Create the radio buttons
                    const tracks = [
                        {value: "random", label: "Random Track"},
                        {value: "music1", label: "Blood Flow"},
                        {value: "music2", label: "Heartbeat"},
                        {value: "music3", label: "Artery Journey"},
                        {value: "music4", label: "Deep Pulse"}
                    ];

                    tracks.forEach(track => {
                        const newLabel = document.createElement("label");
                        const newRadio = document.createElement("input");
                        newRadio.type = "radio";
                        newRadio.name = "musicTrack";
                        newRadio.value = track.value;
                        if (track.value === "random") {
                            newRadio.checked = true; // Default to random
                        }

                        newLabel.appendChild(newRadio);
                        newLabel.appendChild(document.createTextNode(" " + track.label));

                        // Add to the container
                        container.appendChild(newLabel);
                        container.appendChild(document.createElement("br")); // Add line break for better spacing
                    });

                    console.log("Created missing musicTrack radio elements");
                }
            }
        }
    }

    // --- Unified Check for Missing Critical Elements ---
    const criticalElementsMap = {
        // HUD
        scoreDisplay: uiElements.scoreDisplay,
        energyDisplay: uiElements.energyDisplay,
        ammoDisplay: uiElements.ammoDisplay,
        levelDisplay: uiElements.levelDisplay,
        // Screens
        startWindow: uiElements.startWindow, // Assume startWindow is primary
        gameOverScreen: uiElements.gameOverScreen,
        levelListScreen: uiElements.levelListScreen,
        settingsScreen: uiElements.settingsScreen,
        infoScreen: uiElements.infoScreen,
        loadingScreen: uiElements.loadingScreen,
        // Buttons (primarily from startWindow)
        startGameBtn: uiElements.startGameBtn,
        settingsWindowBtn: uiElements.settingsWindowBtn, // Check the one referenced in logs
        infoWindowBtn: uiElements.infoWindowBtn, // Check the one referenced in logs
        restartBtn: uiElements.restartBtn,
        nextLevelBtn: uiElements.nextLevelBtn,
        // Other Controls
        effectsEnabledCheckbox: uiElements.effectsEnabledCheckbox,
        musicEnabledCheckbox: uiElements.musicEnabledCheckbox,
        // Fallback / Optional
        startScreen: uiElements.startScreen, // Referenced in logs as missing
        startWindowLogo: uiElements.startWindowLogo, // Referenced in logs as missing
    };

    const missingElements = Object.entries(criticalElementsMap)
        .filter(([, element]) => !element)
        .map(([key]) => key);

    if (missingElements.length > 0) {
        // Log a single warning with all missing elements
        console.warn(`UI Init Warning - Missing elements: ${missingElements.join(", ")}. Check HTML IDs and ensure 'getUIElementReferences' runs after DOM load.`);
    } else {
        if (DEBUG_MODE) console.log("All critical UI element references obtained.");
    }
}

/**
 * Sets up click event listeners for UI buttons (Start, Restart, Sound Toggle, Level Select, etc.)
 * AND touch listeners for mobile controls.
 * Also adds keyboard navigation for buttons using arrow keys and selection with Space/Enter.
 */
function setupUIEventListeners() {
    // Ensure DOM is fully loaded before trying to set up event listeners
    if (document.readyState !== 'complete') {
        console.log("DOM not fully loaded yet, deferring UI event listener setup");
        // Schedule to run again when DOM is fully loaded
        window.addEventListener('load', setupUIEventListeners, {once: true});
        return;
    }

    // Make this function available globally
    window.setupUIEventListeners = setupUIEventListeners;

    // Ensure UI elements are initialized before setting up event listeners
    getUIElementReferences();

    // Load and apply settings
    loadSettings();
    applySettings();

    // Setup keyboard navigation for buttons
    setupKeyboardNavigation();

    // Start Game Button Listener (from start window)
    if (uiElements.startGameBtn) {
        uiElements.startGameBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Start Game button clicked.");

            // Check if game elements are ready before starting
            if (!ensureGameReadyToStart()) {
                // Show error message
                showGameStartError("Cannot start game: critical components missing.");
                return;
            }

            // Ensure game logic isn't already running
            if (!gameRunning) {
                // Always start with level 1 when clicking Start Game
                gameLevel = 1;

                // Hide start screen
                if (uiElements.startScreen) {
                    hideOverlay(uiElements.startScreen);
                }

                // Also hide start window if it exists
                if (uiElements.startWindow) {
                    hideOverlay(uiElements.startWindow);
                }

                // Ensure audio is activated
                if (typeof ensureMusicPlays === 'function') {
                    ensureMusicPlays();
                }

                // Automatically create a network game without showing invite dialog
                if (typeof createGame === 'function') {
                    try {
                        createGame(false).then(() => { // Pass false to not show invite dialog
                            if (DEBUG_MODE) console.log("Network game created automatically");

                            // Call the main game start function
                            callStartGame()
                                .then(() => {
                                    if (DEBUG_MODE) console.log("Game started successfully");
                                    // Try to ensure audio again after game has started
                                    if (typeof ensureMusicPlays === 'function') {
                                        setTimeout(ensureMusicPlays, 500);
                                    }
                                })
                                .catch(error => {
                                    console.error("FATAL: Error starting game:", error);
                                    showGameStartError("Game engine error: " + error.message);
                                });
                        }).catch(error => {
                            console.warn("Could not create network game, starting in single player mode:", error);
                            // Fall back to regular game start if network game creation fails
                            callStartGame()
                                .then(() => {
                                    if (DEBUG_MODE) console.log("Game started in single player mode");
                                    if (typeof ensureMusicPlays === 'function') {
                                        setTimeout(ensureMusicPlays, 500);
                                    }
                                })
                                .catch(error => {
                                    console.error("FATAL: Error starting game:", error);
                                    showGameStartError("Game engine error: " + error.message);
                                });
                        });
                    } catch (error) {
                        console.warn("Error creating network game, starting in single player mode:", error);
                        // Fall back to regular game start if network game creation fails
                        callStartGame()
                            .then(() => {
                                if (DEBUG_MODE) console.log("Game started in single player mode");
                                if (typeof ensureMusicPlays === 'function') {
                                    setTimeout(ensureMusicPlays, 500);
                                }
                            })
                            .catch(error => {
                                console.error("FATAL: Error starting game:", error);
                                showGameStartError("Game engine error: " + error.message);
                            });
                    }
                } else {
                    // If createGame function is not available, fall back to regular game start
                    callStartGame()
                        .then(() => {
                            if (DEBUG_MODE) console.log("Game started in single player mode (createGame not available)");
                            if (typeof ensureMusicPlays === 'function') {
                                setTimeout(ensureMusicPlays, 500);
                            }
                        })
                        .catch(error => {
                            console.error("FATAL: Error starting game:", error);
                            showGameStartError("Game engine error: " + error.message);
                        });
                }

                // --- CLEANUP DEMO SCENE ---
                if (typeof cleanupDemoScene === 'function') {
                    cleanupDemoScene(window.scene); // zakładamy, że globalny obiekt scene istnieje
                }
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Start Game button not found.");
    }

    // Settings Button Listener (from start window)
    if (uiElements.settingsWindowBtn) {
        uiElements.settingsWindowBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Settings button clicked from start window.");

            // Load settings before showing the settings screen
            loadSettings();

            // Hide start screen
            if (uiElements.startScreen) {
                hideOverlay(uiElements.startScreen);
            }

            // Show settings screen
            if (uiElements.settingsScreen) {
                showOverlay(uiElements.settingsScreen);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Settings Window button not found.");
    }

    // Info Button Listener (from start window)
    if (uiElements.infoWindowBtn) {
        uiElements.infoWindowBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Info button clicked from start window.");

            // Hide start screen
            if (uiElements.startScreen) {
                hideOverlay(uiElements.startScreen);
            }

            // Show info screen
            if (uiElements.infoScreen) {
                showOverlay(uiElements.infoScreen);
                // Populate game info content
                displayGameInfo();
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Info Window button not found.");
    }


    // Level Select Button Listener (on Start Screen)
    if (uiElements.levelSelectBtn) {
        uiElements.levelSelectBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Level select button clicked (from Start Screen).");
            try {
                // Check if levelListScreen exists
                if (!uiElements.levelListScreen) {
                    console.error("levelListScreen is null! Trying to get it again...");
                    levelListScreen = document.getElementById("levelListScreen");
                    if (!uiElements.levelListScreen) {
                        console.error("Still couldn't find levelListScreen element!");
                        return;
                    }
                }

                hideOverlay(uiElements.startScreen);
                populateLevelList(); // Populate the level list UI
                showOverlay(uiElements.levelListScreen);

                // Debug logging to help diagnose issues
                if (DEBUG_MODE) {
                    console.log("Showing level list screen. Ensure levelListContainer and levelListScreen elements are valid.");
                    if (!uiElements.levelListScreen) console.error("levelListScreen element is null!");
                    if (!uiElements.levelListContainer) console.error("levelListContainer element is null!");
                }
            } catch (error) {
                console.error("Error in level select button click handler:", error);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Level select button not found.");
    }

    // Join Game Button Listener (on Start Screen)
    if (document.getElementById("joinGameBtn")) {
        document.getElementById("joinGameBtn").addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Join game button clicked (from Start Screen).");
            try {
                // Call the showJoinGameDialog function from multiplayer.js
                if (typeof showJoinGameDialog === 'function') {
                    showJoinGameDialog();
                } else {
                    console.error("showJoinGameDialog function not found!");
                    // Show error message
                    showNotification("Multiplayer functionality not available", 3000);
                }
            } catch (error) {
                console.error("Error in join game button click handler:", error);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Join game button not found.");
    }

    // Create Multiplayer Game Button removed as per requirements

    // Join Multiplayer Game Button Listener (in Settings Screen)
    const joinMultiplayerGameBtn = document.getElementById("joinMultiplayerGameBtn");
    if (joinMultiplayerGameBtn) {
        joinMultiplayerGameBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Join multiplayer game button clicked (from Settings Screen).");
            try {
                // Hide settings screen
                hideOverlay(uiElements.settingsScreen);

                // Call the showJoinGameDialog function from multiplayer.js
                if (typeof showJoinGameDialog === 'function') {
                    showJoinGameDialog();
                } else {
                    console.error("showJoinGameDialog function not found!");
                    // Show error message
                    showNotification("Multiplayer functionality not available", 3000);
                    // Show settings screen again
                    showOverlay(uiElements.settingsScreen);
                }
            } catch (error) {
                console.error("Error in join multiplayer game button click handler:", error);
                // Show settings screen again
                showOverlay(uiElements.settingsScreen);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Join multiplayer game button not found.");
    }

    // Save Settings Button Listener
    if (uiElements.saveSettingsBtn) {
        uiElements.saveSettingsBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Save settings button clicked.");
            const saveSuccess = saveSettings();

            // Only hide settings screen after a delay to show the confirmation message
            if (saveSuccess) {
                setTimeout(() => {
                    // Hide settings screen using the hideOverlay function
                    if (uiElements.settingsScreen) {
                        hideOverlay(uiElements.settingsScreen);
                    }

                    // Show start screen using the showOverlay function
                    if (uiElements.startScreen) {
                        showOverlay(uiElements.startScreen);
                    }
                }, 1500); // Match the timeout in saveSettings for visual feedback
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Save settings button not found.");
    }

    // Back Button Listener (on Settings Screen)
    if (uiElements.backFromSettingsBtn) {
        uiElements.backFromSettingsBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Back from settings button clicked.");

            // Stop the game if it's running
            if (typeof gameRunning !== 'undefined' && gameRunning) {
                gameRunning = false;
                if (DEBUG_MODE) console.log("Game stopped when returning from settings to start screen");
            }

            // Hide settings screen using the hideOverlay function
            if (uiElements.settingsScreen) {
                hideOverlay(uiElements.settingsScreen);
            }

            // Show start screen using the showOverlay function
            if (uiElements.startScreen) {
                showOverlay(uiElements.startScreen);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Back from settings button not found.");
    }

    // Back Button Listener (on Info Screen)
    if (uiElements.backFromInfoBtn) {
        uiElements.backFromInfoBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Back from info button clicked.");

            // Hide info screen using the hideOverlay function
            if (uiElements.infoScreen) {
                hideOverlay(uiElements.infoScreen);
            }

            // Show start screen using the showOverlay function
            if (uiElements.startScreen) {
                showOverlay(uiElements.startScreen);
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Back from info button not found.");
    }

    // Back Button Listener (on Level List Screen)
    if (uiElements.backToStartBtn) {
        uiElements.backToStartBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Back button clicked.");

            // Hide level list screen
            hideOverlay(uiElements.levelListScreen);

            // Show start screen
            showOverlay(uiElements.startScreen);
        });
    } else if (DEBUG_MODE) {
        console.warn("Back button not found.");
    }

    // Level Select Button Listener (on Game Over Screen)
    if (uiElements.levelSelectFromGameOverBtn) {
        uiElements.levelSelectFromGameOverBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Level select from game over button clicked.");
            hideOverlay(uiElements.gameOverScreen);
            populateLevelList(); // Populate the level list
            showOverlay(uiElements.levelListScreen);
        });
    } else if (DEBUG_MODE) {
        console.warn("Level select from game over button not found.");
    }

    // Restart Button Listener (on Game Over screen)
    if (uiElements.restartBtn) {
        uiElements.restartBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Restart/Retry button clicked.");
            // Hide game over screen immediately, then restart the current level
            hideOverlay(uiElements.gameOverScreen);

            // Automatically create a network game before restarting without showing invite dialog
            if (typeof createGame === 'function') {
                try {
                    createGame(false).then(() => { // Pass false to not show invite dialog
                        if (DEBUG_MODE) console.log("Network game created automatically for restart");
                        startGame(); // Start the game with the current level
                    }).catch(error => {
                        console.warn("Could not create network game for restart, starting in single player mode:", error);
                        startGame(); // Fall back to regular game start
                    });
                } catch (error) {
                    console.warn("Error creating network game for restart, starting in single player mode:", error);
                    startGame(); // Fall back to regular game start
                }
            } else {
                startGame(); // Fall back to regular game start if createGame is not available
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Restart button not found.");
    }

    // Next Level Button Listener (on Game Over screen)
    if (uiElements.nextLevelBtn) {
        uiElements.nextLevelBtn.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Next level button clicked.");
            // Only proceed if the current level is completed
            if (typeof isLevelCompleted === 'function' && isLevelCompleted(gameLevel)) {
                // Increment level and wrap around after level 18
                gameLevel = (gameLevel % 18) + 1;
                hideOverlay(uiElements.gameOverScreen);

                // Automatically create a network game before starting next level without showing invite dialog
                if (typeof createGame === 'function') {
                    try {
                        createGame(false).then(() => { // Pass false to not show invite dialog
                            if (DEBUG_MODE) console.log("Network game created automatically for next level");
                            startGame(); // Start the game with the next level
                        }).catch(error => {
                            console.warn("Could not create network game for next level, starting in single player mode:", error);
                            startGame(); // Fall back to regular game start
                        });
                    } catch (error) {
                        console.warn("Error creating network game for next level, starting in single player mode:", error);
                        startGame(); // Fall back to regular game start
                    }
                } else {
                    startGame(); // Fall back to regular game start if createGame is not available
                }
            } else {
                if (DEBUG_MODE) console.log("Cannot proceed to next level - current level not completed");
            }
        });
    } else if (DEBUG_MODE) {
        console.warn("Next level button not found.");
    }

    // Sound Toggle Button Listener
    if (uiElements.soundControl) {
        // Initialize button text based on current state of both sound and music
        // Load music setting from localStorage if not already set
        if (typeof musicEnabled === 'undefined') {
            musicEnabled = (localStorage.getItem('musicEnabled') !== 'false'); // Default to true
        }

        uiElements.soundControl.textContent = musicEnabled ? "🔊" : "🔇";
        uiElements.soundControl.addEventListener('click', () => {
            if (DEBUG_MODE) console.log("Sound toggle button clicked.");
            toggleSound(); // Call the sound toggle function (in audio.js)
        });
    } else if (DEBUG_MODE) {
        console.warn("Sound control button not found.");
    }

    if (DEBUG_MODE) console.log("UI Event listeners set up.");
}

// Then modify the ensureGameReadyToStart function:
/**
 * Ensures all critical game components are available before starting
 * @returns {boolean} True if game is ready to start
 */
function ensureGameReadyToStart() {
    // Check for scene and engine from window globals
    if (!window.scene || !window.engine) {
        console.error("Scene or engine not initialized!");
        return false;
    }


    // Ensure UI is available
    if (!uiElements || !uiElements.hudElement) {
        console.warn("HUD elements may not be fully initialized");
        // Non-critical, continue anyway
    }

    return true;
}

/**
 * Shows a user-friendly error message when game can't start
 * @param {string} message - The error message to display
 */
function showGameStartError(message) {
    // Create a modal error dialog
    const errorModal = document.createElement('div');
    errorModal.className = 'error-modal';
    errorModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;

    // Create content container
    const errorContent = document.createElement('div');
    errorContent.style.cssText = `
        background-color: #900;
        color: white;
        padding: 20px;
        border-radius: 10px;
        max-width: 80%;
        text-align: center;
    `;

    // Add message
    errorContent.innerHTML = `
        <h2>Game Start Failed</h2>
        <p>${message}</p>
        <p>Please try refreshing the page.</p>
        <button id="errorDismissBtn" style="
            padding: 10px 20px;
            margin-top: 15px;
            background-color: #555;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        ">Dismiss</button>
    `;

    // Add to DOM
    errorModal.appendChild(errorContent);
    document.body.appendChild(errorModal);

    // Add dismiss button handler
    document.getElementById('errorDismissBtn').addEventListener('click', () => {
        errorModal.remove();
    });
}

/**
 * Creates a UI element safely with enhanced error handling.
 * @param {string} elementType - HTML element type (e.g., 'div', 'button')
 * @param {Object} attributes - Element attributes {id, className, style, etc.}
 * @param {string|HTMLElement|Array} content - Inner content (string, element, or array of elements)
 * @param {HTMLElement} parent - Parent element to attach to
 * @param {boolean} prepend - Whether to prepend to parent (instead of append)
 * @returns {HTMLElement|null} - The created element or null if failed
 */
function createUIElement(elementType, attributes = {}, content = null, parent = null, prepend = false) {
    try {
        // Create the element
        const element = document.createElement(elementType);

        // Apply attributes
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'style' && typeof value === 'object') {
                // Handle style object
                Object.entries(value).forEach(([prop, val]) => {
                    element.style[prop] = val;
                });
            } else if (key === 'dataset' && typeof value === 'object') {
                // Handle data attributes
                Object.entries(value).forEach(([dataKey, dataVal]) => {
                    element.dataset[dataKey] = dataVal;
                });
            } else {
                // Regular attributes
                element[key] = value;
            }
        });

        // Add content
        if (content !== null) {
            if (typeof content === 'string') {
                element.textContent = content;
            } else if (content instanceof HTMLElement) {
                element.appendChild(content);
            } else if (Array.isArray(content)) {
                content.forEach(item => {
                    if (typeof item === 'string') {
                        const textNode = document.createTextNode(item);
                        element.appendChild(textNode);
                    } else if (item instanceof HTMLElement) {
                        element.appendChild(item);
                    }
                });
            } else if (typeof content === 'object' && content.html) {
                // HTML content (use with caution)
                element.innerHTML = content.html;
            }
        }

        // Attach to parent
        if (parent instanceof HTMLElement) {
            if (prepend && parent.firstChild) {
                parent.insertBefore(element, parent.firstChild);
            } else {
                parent.appendChild(element);
            }
        }

        return element;
    } catch (error) {
        console.error(`Error creating ${elementType} element:`, error);
        return null;
    }
}

// Then update the ensureCriticalUIElements function to use this helper
function ensureCriticalUIElements() {
    // Example usage for creating the startWindowLogo if missing
    if (!document.getElementById('startWindowLogo')) {
        if (DEBUG_MODE) console.log("Creating missing startWindowLogo element");

        const startWindow = document.getElementById('startWindow');
        if (startWindow) {
            const startWindowContent = startWindow.querySelector('.start-window-content');
            if (startWindowContent) {
                // Create logo div with image inside
                const logoImg = createUIElement('img', {
                    src: './textures/hemolens_logo.jpg',
                    alt: 'Hemolens Logo'
                });

                createUIElement('div', {
                    id: 'startWindowLogo',
                    className: 'start-window-logo'
                }, logoImg, startWindowContent, true); // Prepend to content
            }
        }
    }

    // Similarly robust creation of other elements...
}

// Show an overlay element (like loading, start, game over screens)
function showOverlay(overlayElement) {
    try {
        if (overlayElement) {
            if (DEBUG_MODE) console.log(`Showing overlay: ${overlayElement.id}`);

            // Ensure the element is in the DOM
            if (!document.body.contains(overlayElement)) {
                console.error(`Element with ID ${overlayElement.id} is not in the DOM!`);
                // Try to get it again
                const reacquiredElement = document.getElementById(overlayElement.id);
                if (reacquiredElement) {
                    overlayElement = reacquiredElement;
                    console.log(`Successfully reacquired element with ID ${overlayElement.id}`);
                } else {
                    console.error(`Could not reacquire element with ID ${overlayElement.id}`);
                    return;
                }
            }

            // Set display style
            overlayElement.style.display = 'flex'; // Use flex for centering content

            // Use requestAnimationFrame to ensure display is set before transition
            requestAnimationFrame(() => {
                try {
                    overlayElement.style.opacity = '1';
                } catch (error) {
                    console.error(`Error setting opacity for ${overlayElement.id}:`, error);
                }
            });

            // *** ADDED: Display intro message when start screen is shown ***
            if (overlayElement === uiElements.startScreen) {
                displayIntroMessage();
            }
            // *** END ADDED ***

        } else if (DEBUG_MODE) {
            console.warn("showOverlay called with null element");
            console.trace(); // Show stack trace to help identify where the null element is coming from
        }
    } catch (error) {
        console.error("Error in showOverlay:", error);
    }
}

// Hide an overlay element
function hideOverlay(overlayElement) {
    try {
        if (overlayElement) {
            if (DEBUG_MODE) console.log(`Hiding overlay: ${overlayElement.id}`);

            // Ensure the element is in the DOM
            if (!document.body.contains(overlayElement)) {
                console.error(`Element with ID ${overlayElement.id} is not in the DOM!`);
                // Try to get it again
                const reacquiredElement = document.getElementById(overlayElement.id);
                if (reacquiredElement) {
                    overlayElement = reacquiredElement;
                    console.log(`Successfully reacquired element with ID ${overlayElement.id}`);
                } else {
                    console.error(`Could not reacquire element with ID ${overlayElement.id}`);
                    return;
                }
            }

            // Set opacity to 0 for fade out effect
            try {
                overlayElement.style.opacity = '0';
            } catch (error) {
                console.error(`Error setting opacity for ${overlayElement.id}:`, error);
            }

            // Wait for fade out transition before setting display: none
            // Transition should be defined in CSS (e.g., transition: opacity 0.3s;)
            setTimeout(() => {
                try {
                    // Check if opacity is still 0 before hiding (might have been shown again)
                    if (overlayElement.style.opacity === '0') {
                        overlayElement.style.display = 'none';
                        if (DEBUG_MODE) console.log(`Overlay hidden: ${overlayElement.id}`);
                    } else {
                        if (DEBUG_MODE) console.log(`Overlay not hidden (opacity changed): ${overlayElement.id}`);
                    }
                } catch (error) {
                    console.error(`Error in hideOverlay setTimeout for ${overlayElement.id}:`, error);
                }
            }, 300); // Match timeout with CSS transition duration
        } else if (DEBUG_MODE) {
            console.warn("hideOverlay called with null element");
            console.trace(); // Show stack trace to help identify where the null element is coming from
        }
    } catch (error) {
        console.error("Error in hideOverlay:", error);
    }
}


/**
 * Populates the intro message div on the start screen with a brief game summary.
 * Uses the coronarySegments and atheroscleroticTypes data from globals.js if available.
 */
function displayIntroMessage() {
    console.log("displayIntroMessage called");

    // Ensure UI elements are initialized before displaying intro message
    getUIElementReferences();

    console.log("uiElements.introMessage:", uiElements.introMessage);

    if (!uiElements.introMessage) {
        console.warn("Intro message element (#introMessage) not found!");

        // Try to get the element directly
        const introMessageElement = document.getElementById("introMessage");
        console.log("Direct getElementById:", introMessageElement);

        if (introMessageElement) {
            console.log("Found element directly, using it");
            uiElements.introMessage = introMessageElement;
        } else {
            console.warn("Could not find intro message element even with direct getElementById");
            return; // Cannot display message if element is missing
        }
    }

    // Create a brief summary for the main screen
    let html = "<h2>Game Summary</h2>";
    html += "<p>Navigate through the coronary artery system as a microscopic bunny. Collect energy eggs and avoid or destroy atherosclerotic plaques.</p>";
    html += "<p>Use arrow keys or virtual joystick to move. Press Space or tap the Shoot button to fire lasers at obstacles.</p>";
    html += "<p>For detailed information about the game, click the Information button.</p>";

    try {
        uiElements.introMessage.innerHTML = html;
        console.log("Intro message HTML set successfully");

        // Add a style to make it more visible for debugging
        uiElements.introMessage.style.border = "2px solid red";
        uiElements.introMessage.style.padding = "10px";
        uiElements.introMessage.style.margin = "10px 0";
    } catch (error) {
        console.error("Error setting intro message HTML:", error);
    }

    console.log("Intro message populated.");
}

/**
 * Populates the game info content div with detailed information about the game in English.
 * Uses the coronarySegments and atheroscleroticTypes data from globals.js if available.
 */
function displayGameInfo() {
    // Ensure UI elements are initialized before displaying game info
    getUIElementReferences();

    if (!uiElements.gameInfoContent) {
        if (DEBUG_MODE) console.warn("Game info content element (#gameInfoContent) not found!");
        return; // Cannot display info if element is missing
    }

    // If coronarySegments or atheroscleroticTypes are not available, we'll still show basic info
    const hasGameData = coronarySegments && atheroscleroticTypes;

    let html = "<h2>Bunny Tunnel: Coronary Adventure</h2>";
    html += "<p>Welcome to Bunny Tunnel, an educational game where you navigate through the human coronary artery system as a microscopic bunny. Your mission is to collect energy eggs while avoiding or destroying atherosclerotic plaques.</p>";

    // Game Controls
    html += "<h3>Game Controls</h3>";
    html += "<ul>";
    html += "<li><strong>Movement:</strong> Arrow keys (keyboard) or virtual joystick (mobile)</li>";
    html += "<li><strong>Shoot:</strong> Space bar (keyboard) or Shoot button (mobile)</li>";
    html += "<li><strong>Energy:</strong> Collect eggs to increase energy, avoid collisions to prevent energy loss</li>";
    html += "<li><strong>Game Over:</strong> Occurs when energy reaches zero</li>";
    html += "</ul>";

    // Levels Information
    html += "<h3>Levels: Coronary Artery Segments</h3>";
    html += "<p>The game features 18 levels, each representing a specific segment of the human coronary artery system. Difficulty increases with smaller vessel diameters and higher levels.</p>";

    // Calculate base speed for each level
    const calculateLevelSpeed = (level) => {
        // Based on the formula in gameLogic.js
        const baseSpeed = Config.INITIAL_BASE_SPEED + (level - 1) * 0.002;
        return baseSpeed * 100; // Convert to more readable units
    };

    // Create a table for level information
    html += "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>";
    html += "<tr style='background-color: rgba(0,0,0,0.3);'><th style='padding: 8px; text-align: left;'>Level</th><th style='padding: 8px; text-align: left;'>Segment</th><th style='padding: 8px; text-align: left;'>Diameter</th><th style='padding: 8px; text-align: left;'>Base Speed</th><th style='padding: 8px; text-align: left;'>Difficulty</th></tr>";

    // Sort levels by diameter (from largest to smallest) for progressive difficulty
    const sortedSegments = [...coronarySegments].sort((a, b) => b.diameter - a.diameter);

    // Group levels by difficulty
    sortedSegments.forEach((segment, index) => {
        const rowStyle = index % 2 === 0 ? 'background-color: rgba(0,0,0,0.1);' : '';
        const levelSpeed = calculateLevelSpeed(segment.level).toFixed(1);

        // Determine difficulty based on position in sorted array
        let difficulty, difficultyColor;
        if (index < 6) { // First 6 levels are Easy
            difficulty = "Easy";
            difficultyColor = "#2ecc71"; // Green
        } else if (index < 12) { // Next 6 levels are Medium
            difficulty = "Medium";
            difficultyColor = "#f39c12"; // Orange
        } else { // Last 6 levels are Hard
            difficulty = "Hard";
            difficultyColor = "#e74c3c"; // Red
        }

        html += `<tr style='${rowStyle}'>`;
        html += `<td style='padding: 8px;'><strong>${segment.level}</strong></td>`;
        html += `<td style='padding: 8px;'>${segment.segment}</td>`;
        html += `<td style='padding: 8px;'>${segment.diameter} mm</td>`;
        html += `<td style='padding: 8px;'>${levelSpeed} units/s</td>`;
        html += `<td style='padding: 8px; color: ${difficultyColor};'><strong>${difficulty}</strong></td>`;
        html += "</tr>";
    });

    html += "</table>";
    html += "<p>Narrow sections within levels provide additional challenges and rewards. Speed increases gradually during gameplay.</p>";

    // Obstacles Information
    html += "<h3>Obstacles: Atherosclerotic Plaques</h3>";
    html += "<p>You will encounter various types of atherosclerotic plaques based on the American Heart Association (AHA) classification:</p>";
    html += "<p><strong>Important:</strong> Types 1-3 can be destroyed by shooting, while types 4-6 are indestructible and must be avoided. Each type has a different appearance and impact on gameplay. Pay attention to color and shape!</p>";
    html += "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>";
    html += "<tr style='background-color: rgba(0,0,0,0.3);'><th style='padding: 8px; text-align: left;'>Type</th><th style='padding: 8px; text-align: left;'>Name</th><th style='padding: 8px; text-align: left;'>Properties</th></tr>";

    atheroscleroticTypes.forEach((type, index) => {
        const colorStyle = `color: ${type.color}; font-weight: bold;`;
        const rowStyle = index % 2 === 0 ? 'background-color: rgba(0,0,0,0.1);' : '';
        html += `<tr style='${rowStyle}'>`;
        html += `<td style='padding: 8px;'><span style="${colorStyle}">Type ${type.type}</span></td>`;
        html += `<td style='padding: 8px;'>${type.englishName}</td>`;
        html += `<td style='padding: 8px;'>Size: ${type.size.min.toFixed(1)}-${type.size.max.toFixed(1)}m<br>${type.isDestructible ? 'Destructible (Shoot to destroy)' : 'Indestructible (Must be avoided)'}</td>`;
        html += "</tr>";
    });
    html += "</table>";

    // Game Features
    html += "<h3>Special Game Features</h3>";
    html += "<ul>";
    html += "<li><strong>Energy Eggs:</strong> Collect these to increase your energy level. They come in various colors (yellow, green, blue, purple, teal).</li>";
    html += "<li><strong>Laser System:</strong> Use your laser to destroy certain types of plaques (Types 1-3). Indestructible plaques (Types 4-6) will break into smaller fragments when hit.</li>";
    html += "<li><strong>Wall Plaques:</strong> During slow gameplay periods, atherosclerotic changes on vessel walls may detach and fall like icicles, adding an extra challenge.</li>";
    html += "<li><strong>Blood Cells:</strong> Red blood cells flow through the vessel, simulating blood flow. They move with the bunny and provide visual context.</li>";
    html += "<li><strong>Dynamic Difficulty:</strong> Game speed increases gradually within each level, and vessel diameter varies to create challenging narrow sections.</li>";
    html += "</ul>";

    // Blood Cells Information
    html += "<h3>Blood Cells</h3>";
    html += "<p>The game features different types of blood cells that you'll encounter:</p>";
    html += "<ul>";
    html += "<li><strong>Red Blood Cells (Erythrocytes):</strong> These are the most common cells in blood. They appear as bright red, disc-shaped cells that flow through the vessels. They don't harm the player and are primarily decorative.</li>";
    html += "<li><strong>White Blood Cells (Leukocytes):</strong> These appear as white spheres with protrusions. They will attack the player if you get too close, causing damage. You can destroy them with your laser.</li>";
    html += "<li><strong>Yellow Leukocytes:</strong> These special immune cells look like yellow suns with red centers. They are more aggressive than regular white blood cells, causing more damage when they attack. They can also be destroyed with your laser for extra points.</li>";
    html += "</ul>";

    // Educational Value
    html += "<h3>Educational Value</h3>";
    html += "<p>This game provides an interactive way to learn about:</p>";
    html += "<ul>";
    html += "<li>The structure and anatomy of the coronary artery system</li>";
    html += "<li>Different types of atherosclerotic plaques according to the AHA classification</li>";
    html += "<li>The progression and characteristics of atherosclerosis</li>";
    html += "<li>The challenges of navigating through narrowed blood vessels</li>";
    html += "<li>The different types of blood cells and their appearance</li>";
    html += "</ul>";

    uiElements.gameInfoContent.innerHTML = html;

    if (DEBUG_MODE) console.log("Game info content populated.");
}

// Function to populate the level list
function populateLevelList() {
    try {
        if (DEBUG_MODE) console.log("populateLevelList called");

        // Ensure UI elements are initialized before populating level list
        getUIElementReferences();

        // Check if levelListContainer exists
        if (!uiElements.levelListContainer) {
            console.error("Level list container not found!");
            // Try to get it again in case it wasn't initialized properly
            setLevelListContainer(document.getElementById("levelList"));
            if (!uiElements.levelListContainer) {
                console.error("Still couldn't find levelList element!");
                return;
            }
        }

        // Ensure the element is in the DOM
        if (!document.body.contains(uiElements.levelListContainer)) {
            console.error("levelListContainer is not in the DOM!");
            // Try to get it again
            const reacquiredElement = document.getElementById("levelList");
            if (reacquiredElement) {
                setLevelListContainer(reacquiredElement);
                console.log("Successfully reacquired levelListContainer");
            } else {
                console.error("Could not reacquire levelListContainer");
                return;
            }
        }

        // Clear existing items
        try {
            uiElements.levelListContainer.innerHTML = '';
            if (DEBUG_MODE) console.log("Cleared levelListContainer");
        } catch (error) {
            console.error("Error clearing levelListContainer:", error);
            return;
        }

        // Get completed levels
        let completedLevels = [];
        try {
            completedLevels = typeof getCompletedLevels === 'function' ? getCompletedLevels() : [];
            if (DEBUG_MODE) console.log("Completed levels:", completedLevels);
        } catch (error) {
            console.error("Error getting completed levels:", error);
            // Continue with empty completed levels
        }

        // Check if coronarySegments is available
        if (!coronarySegments || !Array.isArray(coronarySegments)) {
            console.error("coronarySegments is not available or not an array!");
            return;
        }

        // Create level items
        for (let i = 1; i <= 18; i++) {
            try {
                const segment = coronarySegments.find(seg => seg.level === i) || coronarySegments[0];
                const isCompleted = completedLevels.includes(i);
                const isUnlocked = i === 1 || completedLevels.includes(i - 1);

                const levelItem = document.createElement('div');
                levelItem.className = `level-item ${isCompleted ? 'completed' : ''} ${!isUnlocked ? 'locked' : ''}`;
                levelItem.textContent = `${i}: ${segment.segment}`;
                levelItem.dataset.level = i;

                // Add click handler only if level is unlocked
                if (isUnlocked) {
                    levelItem.addEventListener('click', () => {
                        try {
                            // Set the game level and start the game
                            setGameLevel(i);
                            hideOverlay(uiElements.levelListScreen);
                            startGame(); // Dynamic import of startGame
                            if (DEBUG_MODE) console.log(`Level ${i} selected, starting game`);
                        } catch (error) {
                            console.error(`Error in level ${i} click handler:`, error);
                        }
                    });
                } else {
                    // Add tooltip for locked levels
                    levelItem.title = "Complete the previous level to unlock";
                    if (DEBUG_MODE) console.log(`Level ${i} (${segment.segment}) is locked. Click listener skipped.`);
                }

                uiElements.levelListContainer.appendChild(levelItem);
            } catch (error) {
                console.error(`Error creating level item ${i}:`, error);
                // Continue with next level
            }
        }

        if (DEBUG_MODE) console.log(`Level list populated with 18 levels.`);
    } catch (error) {
        console.error("Error in populateLevelList:", error);
    }
}

/**
 * Loads settings from localStorage or uses defaults
 */
// Modify the loadSettings function to include multiplayer settings
function loadSettings() {
    try {
        // Ensure UI elements are initialized before loading settings
        getUIElementReferences();

        // Load settings from localStorage if available
        effectsEnabled = (localStorage.getItem('effectsEnabled') !== 'false'); // Default to true
        musicEnabled = (localStorage.getItem('musicEnabled') !== 'false'); // Default to true
        musicTrack = (localStorage.getItem('musicTrack') || 'random'); // Default to random
        gyroEnabled = (localStorage.getItem('gyroEnabled') === 'true'); // Default to false
        learningModeEnabled = (localStorage.getItem('learningModeEnabled') === 'true'); // Default to false

        // Update UI to reflect current settings
        if (uiElements.effectsEnabledCheckbox) {
            uiElements.effectsEnabledCheckbox.checked = effectsEnabled;
        } else if (DEBUG_MODE) {
            console.warn("effectsEnabledCheckbox not found in loadSettings");
        }

        if (uiElements.musicEnabledCheckbox) {
            uiElements.musicEnabledCheckbox.checked = musicEnabled;
        } else if (DEBUG_MODE) {
            console.warn("musicEnabledCheckbox not found in loadSettings");
        }

        if (uiElements.learningModeEnabledCheckbox) {
            uiElements.learningModeEnabledCheckbox.checked = learningModeEnabled;
        } else if (DEBUG_MODE) {
            console.warn("learningModeEnabledCheckbox not found in loadSettings");
        }

        // Set the correct radio button for music track
        if (uiElements.musicTrackRadios && uiElements.musicTrackRadios.length > 0) {
            for (let i = 0; i < uiElements.musicTrackRadios.length; i++) {
                if (uiElements.musicTrackRadios[i].value === musicTrack) {
                    uiElements.musicTrackRadios[i].checked = true;
                    break;
                }
            }
        } else if (DEBUG_MODE) {
            console.warn("musicTrackRadios not found or empty in loadSettings");
        }

        // Update gyro checkbox
        if (uiElements.gyroEnabledCheckbox) {
            uiElements.gyroEnabledCheckbox.checked = gyroEnabled;
        } else if (DEBUG_MODE) {
            console.warn("gyroEnabledCheckbox not found in loadSettings");
        }

        // Set up multiplayer settings UI
        setupMultiplayerSettings();

        if (DEBUG_MODE) {
            console.log(`Settings loaded: effects=${effectsEnabled}, music=${musicEnabled}, track=${musicTrack}, gyro=${gyroEnabled}, learningMode=${learningModeEnabled}`);
        }
    } catch (error) {
        console.error("Error in loadSettings:", error);
        // Continue with default settings even if UI update fails
    }
}

// Add event handlers for multiplayer settings elements in setupUIEventListeners
function addMultiplayerEventListeners() {
    // Ensure UI elements are initialized before adding multiplayer event listeners
    getUIElementReferences();

    // Set up color selector click events
    const colorOptions = document.querySelectorAll('.color-option');
    if (colorOptions && colorOptions.length > 0) {
        colorOptions.forEach(option => {
            option.addEventListener('click', () => {
                // Remove selected class from all options
                colorOptions.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                option.classList.add('selected');
            });
        });
    }

    // Add these calls to updateConnectionStatus at appropriate times
    // 1. When settings screen is shown
    if (uiElements.settingsBtn) {
        uiElements.settingsBtn.addEventListener('click', () => {
            updateConnectionStatus();
        });
    }

    // 2. Periodically update when settings screen is visible
    setInterval(() => {
        if (uiElements.settingsScreen &&
            uiElements.settingsScreen.style.display !== 'none') {
            updateConnectionStatus();
        }
    }, 5000); // Update every 5 seconds
}

/**
 * Saves settings to localStorage and applies them
 */
function saveSettings() {
    try {
        // Ensure UI elements are initialized before saving settings
        getUIElementReferences();

        // Get values from UI for existing settings
        if (uiElements.effectsEnabledCheckbox) {
            effectsEnabled = uiElements.effectsEnabledCheckbox.checked;
            localStorage.setItem('effectsEnabled', effectsEnabled);
        }

        if (uiElements.musicEnabledCheckbox) {
            musicEnabled = uiElements.musicEnabledCheckbox.checked;
            localStorage.setItem('musicEnabled', musicEnabled);
        }

        if (uiElements.gyroEnabledCheckbox) {
            gyroEnabled = uiElements.gyroEnabledCheckbox.checked;
            localStorage.setItem('gyroEnabled', gyroEnabled);
        }

        if (uiElements.learningModeEnabledCheckbox) {
            learningModeEnabled = uiElements.learningModeEnabledCheckbox.checked;
            localStorage.setItem('learningModeEnabled', learningModeEnabled);
        }

        // Get selected music track
        let selectedTrack = 'random';
        if (uiElements.musicTrackRadios && uiElements.musicTrackRadios.length > 0) {
            for (let i = 0; i < uiElements.musicTrackRadios.length; i++) {
                if (uiElements.musicTrackRadios[i].checked) {
                    selectedTrack = uiElements.musicTrackRadios[i].value;
                    break;
                }
            }
        }
        musicTrack = selectedTrack;
        localStorage.setItem('musicTrack', musicTrack);

        // Save multiplayer settings
        saveMultiplayerSettings();

        // Apply settings
        applySettings();

        if (DEBUG_MODE) {
            console.log(`Settings saved: effects=${effectsEnabled}, music=${musicEnabled}, track=${musicTrack}, learningMode=${learningModeEnabled}`);
        }

        // Show a visual confirmation that settings were saved
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            const originalText = saveBtn.textContent;
            saveBtn.textContent = "✓ Saved!";
            saveBtn.style.backgroundColor = "#4CAF50";

            // Reset button text after a short delay
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.style.backgroundColor = "";
            }, 1500);
        }

        return true; // Return true to indicate success
    } catch (error) {
        console.error("Error saving settings:", error);

        // Show error message
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            saveBtn.textContent = "❌ Error!";
            saveBtn.style.backgroundColor = "#f44336";

            // Reset button text after a short delay
            setTimeout(() => {
                saveBtn.textContent = "Save Settings";
                saveBtn.style.backgroundColor = "";
            }, 1500);
        }

        return false; // Return false to indicate failure
    }
}

/**
 * Applies current settings to the game
 */
function applySettings() {
    // Ensure UI elements are initialized before applying settings
    getUIElementReferences();

    // Apply effects setting
    // Ensure effectsEnabled is a boolean
    effectsEnabled = (effectsEnabled === true);
    if (DEBUG_MODE) console.log(`Effects ${effectsEnabled ? 'enabled' : 'disabled'} in applySettings`);

    // Apply music setting
    // Ensure musicEnabled is a boolean
    musicEnabled = (musicEnabled === true);
    if (musicEnabled) {
        // Start playing music if it's not already playing
        try {
            playBackgroundMusic();
        } catch (error) {
            console.error("Error playing background music:", error);
        }
    } else {
        // Stop music if it's playing
        try {
            stopBackgroundMusic();
        } catch (error) {
            console.error("Error stopping background music:", error);
        }
    }

    // Apply gyro setting
    // Ensure gyroEnabled is a boolean
    gyroEnabled = (gyroEnabled === true);

    // Apply learning mode setting
    // Ensure learningModeEnabled is a boolean
    learningModeEnabled = (learningModeEnabled === true);
    // Make it available globally for other modules
    window.learningModeEnabled = learningModeEnabled;
    if (DEBUG_MODE) console.log(`Learning mode ${learningModeEnabled ? 'enabled' : 'disabled'} in applySettings`);

    // Apply gyro setting based on checkbox
    if (gyroEnabled) {
        // Enable gyroscope if available
        if (window.DeviceOrientationEvent) {
            if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                // iOS 13+ requires permission
                DeviceOrientationEvent.requestPermission()
                    .then(permissionState => {
                        if (permissionState === 'granted') {
                            if (DEBUG_MODE) console.log("DeviceOrientation permission granted (iOS).");
                            window.addEventListener('deviceorientation', handleDeviceOrientation);
                            deviceOrientation.permissionGranted = true;
                        } else {
                            if (DEBUG_MODE) console.warn("DeviceOrientation permission denied (iOS).");
                            deviceOrientation.permissionGranted = false;
                            // Update checkbox to reflect actual state
                            if (uiElements.gyroEnabledCheckbox) {
                                uiElements.gyroEnabledCheckbox.checked = false;
                                gyroEnabled = false;
                                localStorage.setItem('gyroEnabled', false);
                            }
                        }
                    })
                    .catch(error => {
                        console.error("Error requesting DeviceOrientation permission (iOS):", error);
                        deviceOrientation.permissionGranted = false;
                        // Update checkbox to reflect actual state
                        if (uiElements.gyroEnabledCheckbox) {
                            uiElements.gyroEnabledCheckbox.checked = false;
                            gyroEnabled = false;
                            localStorage.setItem('gyroEnabled', false);
                        }
                    });
            } else {
                // For other browsers/older iOS
                if (DEBUG_MODE) console.log("Adding standard deviceorientation listener.");
                window.addEventListener('deviceorientation', handleDeviceOrientation);
                deviceOrientation.permissionGranted = true;
            }
        } else {
            if (DEBUG_MODE) console.warn("DeviceOrientationEvent API not supported by this browser/device.");
            deviceOrientation.available = false;
            // Update checkbox to reflect actual state
            if (uiElements.gyroEnabledCheckbox) {
                uiElements.gyroEnabledCheckbox.checked = false;
                gyroEnabled = false;
                localStorage.setItem('gyroEnabled', false);
            }
        }
    } else {
        // Disable gyroscope
        window.removeEventListener('deviceorientation', handleDeviceOrientation);
        deviceOrientation.permissionGranted = false;
    }

    // Apply multiplayer settings
    // multiplayerEnabled is always true, but we can control whether others can join
    // The multiplayer module will check the allowJoining setting when handling join requests
    // The playerUsername will be used when sending messages to other players

    // Update UI to reflect current settings
    if (DEBUG_MODE) {
        console.log(`Applied settings: effects=${effectsEnabled}, music=${musicEnabled}, track=${musicTrack}, gyro=${gyroEnabled}, allowJoining=${allowJoining}, username=${playerUsername}`);
    }
}

// Note: playBackgroundMusic and stopBackgroundMusic functions are now imported from audio.js

/**
 * Handles device orientation events for gyroscope control
 */
function handleDeviceOrientation(event) {
    // Ogranicz wartości beta i gamma, aby uniknąć ekstremalnych przechyłów
    // Beta (przód/tył): Interesuje nas zakres np. -60 do 60 stopni
    // Gamma (lewo/prawo): Interesuje nas zakres np. -60 do 60 stopni
    const betaThreshold = 60;
    const gammaThreshold = 60;

    deviceOrientation.beta = Math.max(-betaThreshold, Math.min(betaThreshold, event.beta || 0));
    deviceOrientation.gamma = Math.max(-gammaThreshold, Math.min(gammaThreshold, event.gamma || 0));
    deviceOrientation.alpha = event.alpha || 0; // Zazwyczaj niepotrzebne do sterowania tilt
    deviceOrientation.available = true; // Oznacz jako dostępne, skoro event działa
}

/**
 * Sets up keyboard navigation for buttons using arrow keys and selection with Space/Enter.
 */
function setupKeyboardNavigation() {
    // Ensure UI elements are initialized before setting up keyboard navigation
    getUIElementReferences();

    // Current active screen
    let currentScreen = null;
    // Currently focused button index
    let focusedButtonIndex = 0;
    // Array to store buttons of the current screen
    let currentButtons = [];

    // Function to update the current screen and its buttons
    function updateCurrentScreen() {
        // Determine which screen is currently visible
        if (uiElements.startWindow && uiElements.startWindow.style.display !== 'none') {
            currentScreen = uiElements.startWindow;
            currentButtons = Array.from(uiElements.startWindow.querySelectorAll('button'));
        } else if (uiElements.startScreen && uiElements.startScreen.style.display !== 'none') {
            currentScreen = uiElements.startScreen;
            currentButtons = Array.from(uiElements.startScreen.querySelectorAll('button'));
        } else if (uiElements.gameOverScreen && uiElements.gameOverScreen.style.display !== 'none') {
            currentScreen = uiElements.gameOverScreen;
            currentButtons = Array.from(uiElements.gameOverScreen.querySelectorAll('button'));
        } else if (uiElements.levelListScreen && uiElements.levelListScreen.style.display !== 'none') {
            currentScreen = uiElements.levelListScreen;
            // Include both buttons and level items (div.level-item) for keyboard navigation
            const buttons = Array.from(uiElements.levelListScreen.querySelectorAll('button'));
            const levelItems = Array.from(uiElements.levelListScreen.querySelectorAll('.level-item:not(.locked)'));
            currentButtons = [...buttons, ...levelItems];
        } else if (uiElements.infoScreen && uiElements.infoScreen.style.display !== 'none') {
            currentScreen = uiElements.infoScreen;
            currentButtons = Array.from(uiElements.infoScreen.querySelectorAll('button'));
        } else if (uiElements.settingsScreen && uiElements.settingsScreen.style.display !== 'none') {
            currentScreen = uiElements.settingsScreen;
            // Get all interactive elements in the settings screen
            const allInteractiveElements = Array.from(uiElements.settingsScreen.querySelectorAll('button, input[type="checkbox"], input[type="radio"], input[type="text"], .color-option'));

            // Sort elements based on their position in the DOM for a logical tab order
            allInteractiveElements.sort((a, b) => {
                // Get the position of each element
                const aRect = a.getBoundingClientRect();
                const bRect = b.getBoundingClientRect();

                // First sort by vertical position (top to bottom)
                if (Math.abs(aRect.top - bRect.top) > 10) { // Use a small threshold to group elements in the same row
                    return aRect.top - bRect.top;
                }

                // If elements are in the same row, sort by horizontal position (left to right)
                return aRect.left - bRect.left;
            });

            currentButtons = allInteractiveElements;
        } else {
            currentScreen = null;
            currentButtons = [];
        }

        // Reset focus index if out of bounds
        if (focusedButtonIndex >= currentButtons.length) {
            focusedButtonIndex = 0;
        }

        // Apply focus to the current button
        updateButtonFocus();
    }

    // Function to update button focus
    function updateButtonFocus() {
        // Remove focus from all buttons
        currentButtons.forEach(button => {
            button.classList.remove('button-focused');
        });

        // Add focus to the current button if available
        if (currentButtons.length > 0 && focusedButtonIndex >= 0 && focusedButtonIndex < currentButtons.length) {
            currentButtons[focusedButtonIndex].classList.add('button-focused');
        }
    }

    // Add keyboard event listener
    document.addEventListener('keydown', (event) => {
        // Only process if a screen is active and has buttons
        if (!currentScreen || currentButtons.length === 0) {
            updateCurrentScreen();
            return;
        }

        switch (event.key) {
            case 'ArrowUp':
            case 'ArrowLeft':
                // Move focus to the previous button
                focusedButtonIndex = (focusedButtonIndex - 1 + currentButtons.length) % currentButtons.length;
                updateButtonFocus();
                event.preventDefault();
                break;

            case 'ArrowDown':
            case 'ArrowRight':
                // Move focus to the next button
                focusedButtonIndex = (focusedButtonIndex + 1) % currentButtons.length;
                updateButtonFocus();
                event.preventDefault();
                break;

            case 'Enter':
            case ' ': // Space
                // Activate the focused button
                if (currentButtons.length > 0 && focusedButtonIndex >= 0 && focusedButtonIndex < currentButtons.length) {
                    currentButtons[focusedButtonIndex].click();
                }
                event.preventDefault();
                break;

            case 'Escape':
                // Find and click a "back" button if available
                const backButton = currentButtons.find(button =>
                    button.id === 'backFromInfoBtn' ||
                    button.id === 'backToStartBtn' ||
                    button.textContent.toLowerCase().includes('back'));

                if (backButton) {
                    backButton.click();
                }
                event.preventDefault();
                break;
        }
    });

    // Add CSS for focused buttons
    const style = document.createElement('style');
    style.textContent = `
        .button-focused {
            outline: 3px solid #ffcc00 !important;
            box-shadow: 0 0 10px #ffcc00 !important;
        }
    `;
    document.head.appendChild(style);

    // Initialize the current screen
    updateCurrentScreen();

    // Update current screen when visibility changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                updateCurrentScreen();
            }
        });
    });

    // Observe all overlay screens for style changes
    [uiElements.startWindow, uiElements.startScreen, uiElements.gameOverScreen, uiElements.levelListScreen, uiElements.infoScreen, uiElements.settingsScreen].forEach(screen => {
        if (screen) {
            observer.observe(screen, {attributes: true});
        }
    });

    // Apply settings when the game starts
    applySettings();
}

// W ui.js, dodaj funkcję inicjalizującą elementy HUD

function initializeHUD() {
    console.log("Using existing HUD elements...");

    // Show the existing HUD
    const existingHud = document.getElementById('hud');
    if (existingHud) {
        existingHud.style.display = 'block';
    }
}

// No need to call this on DOMContentLoaded as it's called from main.js

// --- DEMO SCENE INTEGRATION ---
// Using global references instead of import
// Functions from demoScene.js should be globally available

// --- DEMO SCENE HOOKS ---
// Funkcja do pokazywania ekranu startowego/menu głównego
function showMainMenu(scene) {
    if (uiElements.startScreen) {
        showOverlay(uiElements.startScreen);
    }
    // Inicjalizuj demoScene jako tło menu
    if (scene && typeof window.initDemoScene === 'function') {
        window.initDemoScene(scene);
    } else {
        console.warn("Demo scene initialization function not found");
    }
}

// --- END OF FILE ui.js ---
