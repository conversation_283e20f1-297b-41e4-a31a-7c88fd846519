// --- START OF FILE input.js ---

// --- <PERSON><PERSON><PERSON><PERSON> Inputu ---

function initializeInputHandling() {
    console.log("=== INITIALIZING INPUT HANDLING ===");

    // Note: Input listeners are already set up by setupInputListeners() at the end of this file
    // This function is kept for compatibility but doesn't add duplicate listeners

    console.log("Input handling initialized (listeners already active)");
}

function handleKeyDown(event) {
    if (!gameRunning) return;

    keysPressed[event.code] = true;

    switch(event.code) {
        case 'KeyP':
        case 'Escape':
            event.preventDefault();
            pauseGame();
            break;
        case 'KeyV':
            event.preventDefault();
            toggleCameraMode();
            break;
        case 'Space':
            event.preventDefault();
            if (typeof shootLaser === 'function') {
                shootLaser();
            }
            break;
    }
}

function handleKeyUp(event) {
    keysPressed[event.code] = false;
}

// Poprawiona funkcja toggle kamery
function toggleCameraMode() {
    console.log("=== TOGGLING CAMERA MODE ===");

    if (!camera || !bunnyCollider) {
        console.warn("Camera or player not available");
        return;
    }

    // Przełącz między FPP a TPP
    if (typeof cameraMode === 'undefined') {
        window.cameraMode = 'FPP';
    }

    if (cameraMode === 'FPP') {
        cameraMode = 'TPP';
        console.log("Switched to Third Person View");
    } else {
        cameraMode = 'FPP';
        console.log("Switched to First Person View");
    }

    // Resetuj pozycję kamery
    if (typeof resetCameraPosition === 'function') {
        resetCameraPosition();
    }
}

// Setup keyboard listeners
function setupInputListeners() {
    // Key Down Event
    window.addEventListener('keydown', (event) => {
        // Ignore repeated events from holding down a key
        if (event.repeat) return;

        // Track pressed keys by both 'key' (e.g., 'a') and 'code' (e.g., 'KeyA')
        // 'code' is generally preferred for layout independence
        keysPressed[event.key.toLowerCase()] = true; // Store lowercase key name
        keysPressed[event.code] = true;
        // if (DEBUG_MODE) console.log(`KeyDown: key='${event.key}', code='${event.code}'`);

        // Handle shooting on Spacebar press (only if game is running)
        if (event.code === 'Space' && gameRunning) {
            shootLaser(); // Call shoot function (defined in player.js)
        }

        // Resetowanie pozycji kamery klawiszem C
        if (event.code === 'KeyC' && gameRunning && typeof resetCameraPosition === 'function') {
            resetCameraPosition();
            if (DEBUG_MODE) console.log("Camera position reset requested via keyboard");
        }

        // Przegląd tunelu klawiszem O
        if (event.code === 'KeyO' && typeof showTunnelOverview === 'function') {
            showTunnelOverview();
            if (DEBUG_MODE) console.log("Tunnel overview requested via keyboard");
        }

        // Obsługa pauzy klawiszem P
        if (event.code === 'KeyP') {
            togglePause();
            if (DEBUG_MODE) console.log("Pause toggled via keyboard");
        }

        // Obsługa wyjścia lub menu klawiszem Escape
        if (event.code === 'Escape') {
            handleEscape();
            if (DEBUG_MODE) console.log("Escape handled via keyboard");
        }

        // Prevent default browser actions for keys used in gameplay
        // (e.g., arrow keys scrolling the page, spacebar scrolling)
        const gameControlCodes = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space', 'KeyW', 'KeyA', 'KeyS', 'KeyD', 'KeyC', 'KeyO', 'KeyP', 'Escape'];
        if (gameControlCodes.includes(event.code)) {
            event.preventDefault(); // Stop browser's default behavior
        }
    });

    // Key Up Event
    window.addEventListener('keyup', (event) => {
        // Update tracking state
        keysPressed[event.key.toLowerCase()] = false;
        keysPressed[event.code] = false;
        // if (DEBUG_MODE) console.log(`KeyUp: key='${event.key}', code='${event.code}'`);
    });

    // Window Blur Event (e.g., user switches tabs)
    window.addEventListener('blur', () => {
        // Reset all key states to false to prevent "stuck" keys
        // when the player returns to the tab
        Object.keys(keysPressed).forEach(key => keysPressed[key] = false);
        if (DEBUG_MODE) console.log("Window blurred, resetting keysPressed state.");
        // Optionally pause the game here if desired
        // if (gameRunning) { pauseGame(); }
    });

    setupDeviceOrientationListener();

    // Window Focus Event (optional)
    // window.addEventListener('focus', () => {
    //     if (DEBUG_MODE) console.log("Window focused.");
    //     // Optionally unpause game if it was paused on blur
    //     // if (wasPausedOnBlur) { resumeGame(); }
    // });

    if (DEBUG_MODE) console.log("Input listeners set up.");
}


// --- Device Orientation Handling ---
// handleDeviceOrientation function moved to ui.js

function setupDeviceOrientationListener() {
    if (window.DeviceOrientationEvent) {
        if (DEBUG_MODE) console.log("DeviceOrientationEvent API available.");
        deviceOrientation.available = true; // Potencjalnie dostępne

        // Gyro functionality is now handled in the settings menu in ui.js
    } else {
        if (DEBUG_MODE) console.warn("DeviceOrientationEvent API not supported by this browser/device.");
        deviceOrientation.available = false;
    }
}

// --- Initialization ---
// Call the setup function immediately to attach listeners when the script loads
setupInputListeners();

// --- Funkcje obsługi pauzy i wyjścia ---

/**
 * Przełącza stan pauzy gry
 */
function togglePause() {
    if (typeof window.gameState !== 'undefined' && typeof window.pauseGame === 'function') {
        // Jeśli mamy dostęp do gameState i pauseGame, użyj ich
        window.pauseGame(!window.gameState.paused);

        // Pokaż lub ukryj menu pauzy
        if (window.gameState.paused) {
            showPauseMenu();
        } else {
            hidePauseMenu();
        }
    } else {
        // Fallback jeśli nie mamy dostępu do gameState
        if (gameRunning) {
            gameRunning = false;
            showPauseMenu();
        } else {
            gameRunning = true;
            hidePauseMenu();
        }
    }
}

/**
 * Obsługuje naciśnięcie klawisza Escape
 */
function handleEscape() {
    // Jeśli gra jest w trakcie, pokaż menu pauzy
    if (gameRunning) {
        togglePause(); // Pauzuj grę i pokaż menu
    } else {
        // Jeśli menu pauzy jest już widoczne, ukryj je i wznów grę
        if (document.getElementById('pauseMenu') &&
            document.getElementById('pauseMenu').style.display !== 'none') {
            togglePause();
        } else {
            // Jeśli jesteśmy na innym ekranie (np. game over), wróć do menu głównego
            if (typeof showMainMenu === 'function' && scene) {
                showMainMenu(scene);
            }
        }
    }
}

/**
 * Tworzy i wyświetla menu pauzy
 */
function showPauseMenu() {
    // Sprawdź, czy menu pauzy już istnieje
    let pauseMenu = document.getElementById('pauseMenu');

    if (!pauseMenu) {
        // Utwórz menu pauzy
        pauseMenu = document.createElement('div');
        pauseMenu.id = 'pauseMenu';
        pauseMenu.style.position = 'absolute';
        pauseMenu.style.top = '50%';
        pauseMenu.style.left = '50%';
        pauseMenu.style.transform = 'translate(-50%, -50%)';
        pauseMenu.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        pauseMenu.style.color = 'white';
        pauseMenu.style.padding = '20px';
        pauseMenu.style.borderRadius = '10px';
        pauseMenu.style.textAlign = 'center';
        pauseMenu.style.zIndex = '1000';
        pauseMenu.style.minWidth = '300px';

        // Dodaj tytuł
        const title = document.createElement('h2');
        title.textContent = 'PAUZA';
        pauseMenu.appendChild(title);

        // Dodaj przyciski
        const resumeBtn = document.createElement('button');
        resumeBtn.textContent = 'Wznów grę';
        resumeBtn.style.margin = '10px';
        resumeBtn.style.padding = '10px 20px';
        resumeBtn.style.backgroundColor = '#4CAF50';
        resumeBtn.style.color = 'white';
        resumeBtn.style.border = 'none';
        resumeBtn.style.borderRadius = '5px';
        resumeBtn.style.cursor = 'pointer';
        resumeBtn.onclick = togglePause;
        pauseMenu.appendChild(resumeBtn);

        const mainMenuBtn = document.createElement('button');
        mainMenuBtn.textContent = 'Menu główne';
        mainMenuBtn.style.margin = '10px';
        mainMenuBtn.style.padding = '10px 20px';
        mainMenuBtn.style.backgroundColor = '#f44336';
        mainMenuBtn.style.color = 'white';
        mainMenuBtn.style.border = 'none';
        mainMenuBtn.style.borderRadius = '5px';
        mainMenuBtn.style.cursor = 'pointer';
        mainMenuBtn.onclick = function() {
            hidePauseMenu();
            if (typeof showMainMenu === 'function' && scene) {
                showMainMenu(scene);
            }
            // Upewnij się, że gra jest zatrzymana
            gameRunning = false;
            if (typeof window.gameState !== 'undefined' && typeof window.pauseGame === 'function') {
                window.pauseGame(true);
            }
        };
        pauseMenu.appendChild(mainMenuBtn);

        // Dodaj menu do dokumentu
        document.body.appendChild(pauseMenu);
    } else {
        // Pokaż istniejące menu
        pauseMenu.style.display = 'block';
    }
}

/**
 * Ukrywa menu pauzy
 */
function hidePauseMenu() {
    const pauseMenu = document.getElementById('pauseMenu');
    if (pauseMenu) {
        pauseMenu.style.display = 'none';
    }
}

// Eksportuj funkcje do globalnego obiektu window
window.togglePause = togglePause;
window.handleEscape = handleEscape;
window.showPauseMenu = showPauseMenu;
window.hidePauseMenu = hidePauseMenu;

// --- END OF FILE input.js ---
