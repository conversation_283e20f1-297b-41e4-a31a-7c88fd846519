// player.js - z użyciem poolingu dla laserów
// Tryb kamery: 'TPP' (trz<PERSON><PERSON>so<PERSON>) lub 'FPP' (pier<PERSON><PERSON> osoba)
let cameraMode = 'FPP';

// Funkcje dla puli laserów
function createLaserForPool() {
    if (!scene) return null;

    // Utwórz pojedynczy laser (prosty cylinder) - zwiększony rozmiar dla lepszej widoczności
    const laser = BABYLON.MeshBuilder.CreateCylinder("laser_pool", {
        height: 2.0,  // Zwiększono długość z 1.0 do 2.0
        diameter: 0.15, // Zwiększono średnicy z 0.1 do 0.15
        tessellation: 8
    }, scene);

    // Obróć cylinder o 90 stopni, aby był poziomo (wzdłuż osi Z)
    // zamiast pionowo (wzdłuż osi Y)
    laser.rotation.x = Math.PI / 2;

    // Materiał lasera (emitujący, ignoruje oświetlenie sceny)
    const laserMaterial = new BABYLON.StandardMaterial("laserMat_pool", scene);
    laserMaterial.diffuseColor = new BABYLON.Color3(0.3, 1, 0.3); // Jasniejsza zielonkawa podstawa
    laserMaterial.emissiveColor = new BABYLON.Color3(1, 1, 1); // Bardzo jasna biała poświata dla lepszej widoczności
    laserMaterial.disableLighting = true; // Niereagujący na światła
    laserMaterial.backFaceCulling = false; // Widoczny z obu stron
    laser.material = laserMaterial;

    // Ukryj początkowo
    laser.setEnabled(false);

    return {
        mesh: laser,
        direction: new BABYLON.Vector3(0, 0, -1),
        speed: Config.LASER_SPEED,
        creationTime: 0
    };
}

function resetLaser(laser) {
    if (!laser || !laser.mesh) return laser;

    // Resetuj pozycję
    laser.mesh.position = new BABYLON.Vector3(0, 0, 0);

    // Resetuj orientację, ale zachowaj obrót o 90 stopni wokół osi X
    // Usuwamy rotationQuaternion, aby używać rotation
    laser.mesh.rotationQuaternion = null;
    laser.mesh.rotation = new BABYLON.Vector3(Math.PI / 2, 0, 0);

    laser.creationTime = Date.now();

    return laser;
}

// Inicjalizacja puli laserów
function initLaserPool() {
    // Utwórz pulę laserów (mniejszą, bo lasery są rzadziej używane niż krwinki)
    poolManager.createPool("laser", 10, createLaserForPool, resetLaser);

    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Laser pool initialized");
}

// Funkcja strzału z użyciem puli
function shootLaser() {
    const now = Date.now();
    // Sprawdź warunki: gra uruchomiona, kamera dotarła do gracza, amunicja, cooldown, scena, istnieje kolizyjna bryła gracza
    if (!gameRunning || !cameraReachedPlayer || ammo <= 0 || now - lastShotTime < Config.SHOT_COOLDOWN || !scene || !bunnyCollider) {
        return;
    }

    ammo--;
    // Używamy settera dla lastShotTime
    lastShotTime = now;
    updateHUD(); // Natychmiastowa aktualizacja UI

    playSoundEffect(laserSound); // Odtwórz dźwięk

    // Pobierz laser z puli
    const laser = poolManager.getObject("laser");
    if (!laser) {
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.warn("Failed to get laser from pool");
        return;
    }

    // Oblicz pozycję startową i kierunek na podstawie kolizyjnej bryły gracza
    // Kierunek zgodny z przodem gracza (osi Z) - laser leci do przodu
    // Usunięto .negate() ponieważ model królika teraz patrzy do przodu
    const forwardVector = bunnyCollider.getDirection(BABYLON.Axis.Z).normalize();
    // Początek strzału bliżej gracza, ale nie za blisko (np. 0.6 zamiast 0.2)
    laser.mesh.position = bunnyCollider.getAbsolutePosition().add(forwardVector.scale(0.6));
    laser.direction = forwardVector.clone();
    // Upewnij się, że laser jest widoczny
    laser.mesh.setEnabled(true);

    // Debug: Sprawdź właściwości lasera
    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
        console.log("Laser created:", {
            position: laser.mesh.position,
            direction: laser.direction,
            enabled: laser.mesh.isEnabled(),
            visible: laser.mesh.isVisible,
            material: laser.mesh.material ? "present" : "missing",
            scaling: laser.mesh.scaling
        });
    }

    // Ustaw orientację lasera (cylindra), aby wskazywał w kierunku strzału
    try {
        const targetPoint = laser.mesh.position.add(forwardVector.scale(10));
        laser.mesh.rotationQuaternion = null;
        laser.mesh.lookAt(targetPoint);
        laser.mesh.rotate(BABYLON.Axis.X, Math.PI / 2, BABYLON.Space.LOCAL);
    } catch (e) {
        console.error("Error setting laser rotation:", e);
        laser.mesh.rotationQuaternion = null;
        laser.mesh.rotation = new BABYLON.Vector3(Math.PI / 2, 0, 0);
    }

    // Dodaj dane lasera do tablicy aktywnych laserów
    lasers.push(laser);
}

// --- Gracz, Kamera, Ruch ---

async function createBunny(scene) {
    try {
        // Collider Box (Invisible) - Reduced size to fit better in blood vessels
        const newBunnyCollider = BABYLON.MeshBuilder.CreateBox("bunnyCollider", {
            width: 0.5,
            depth: 0.5,
            height: 0.7
        }, scene);

        // Set properties before setting global reference to avoid null reference issues
        newBunnyCollider.isVisible = false;
        newBunnyCollider.isPickable = false;
        newBunnyCollider.checkCollisions = false;

        // Update local reference after setting global
        bunnyCollider = newBunnyCollider;

        // Check if tunnelSections is defined and has elements
        if (typeof tunnelSections !== 'undefined' && Array.isArray(tunnelSections) && tunnelSections.length > 0) {
            // Make sure the last section has a centerPoint
            if (tunnelSections[tunnelSections.length - 1] && tunnelSections[tunnelSections.length - 1].centerPoint) {
                bunnyCollider.position = tunnelSections[tunnelSections.length - 1].centerPoint.clone();
            } else {
                // Fallback position if centerPoint is missing
                bunnyCollider.position = new BABYLON.Vector3(0, 0, 5);
                if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.warn("Last tunnel section missing centerPoint, using fallback position");
            }
        } else {
            // Fallback position if tunnelSections is empty or undefined
            bunnyCollider.position = new BABYLON.Vector3(0, 0, 5);
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.warn("tunnelSections is empty or undefined, using fallback position");
        }

        bunnyCollider.rotationQuaternion = BABYLON.Quaternion.Identity();
    } catch (error) {
        console.error("Error creating bunny collider:", error);
        throw new Error("Failed to create bunny collider");
    }

    // Load visible model
    try {
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Loading bunny model...");
        const modelFileName = "20424_Sitting_inside_an_egg_Texture.obj";
        const modelPath = "./rabbit/";

        // Check if SceneLoader is available
        if (!BABYLON.SceneLoader) {
            console.error("BABYLON.SceneLoader not available. Creating fallback bunny.");
            await createFallbackBunny(scene);
            return; // Exit early
        }

        // Check if bunnyCollider exists before proceeding
        if (!bunnyCollider) {
            console.error("bunnyCollider is null. Cannot load bunny model.");
            throw new Error("bunnyCollider is null");
        }

        let newBunny = null;
        try {
            const result = await BABYLON.SceneLoader.ImportMeshAsync("", modelPath, modelFileName, scene);
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log(`Loaded ${result.meshes.length} meshes from bunny model.`);

            if (result.meshes.length > 0) {
                newBunny = result.meshes[0];
                newBunny.name = "loadedBunny";
                newBunny.parent = bunnyCollider;

                // Update local reference after setting global
                bunny = newBunny;
            } else {
                throw new Error("No meshes loaded from bunny model");
            }
        } catch (loadError) {
            console.error("Error loading bunny model:", loadError);
            await createFallbackBunny(scene);
            // Update local reference after fallback creation
            return; // Exit early
        }

        // Apply scaling and positioning to the bunny model
        if (bunny) {
            const scaleFactor = 0.03; // Jeszcze bardziej zmniejszony model królika

            // Position relative to collider center
            bunny.scaling.scaleInPlace(scaleFactor);

            // *** ADDED/MODIFIED: Manual Offset to center visual model ***
            // Apply corrective offset *after* parenting and scaling.
            // Adjust the X value based on visual observation until lasers appear centered.
            // A negative X value shifts the visual model to the right relative to the collider.
            // A positive X value shifts the visual model to the left relative to the collider.

            bunny.position = new BABYLON.Vector3(-0.15, 0.2, 0.2); // Przesunięcie królika bardziej w lewo, aby skorygować pozycję gracza
            // *** END OFFSET ***

            bunny.rotationQuaternion = null; // Inherit rotation initially

            // Changed rotation to make bunny face forward instead of backward
            bunny.rotation.y = 0; // No rotation around Y axis so bunny faces forward
            bunny.rotation.x = Math.PI * 1.4;
        } else {
            console.warn("Bunny model not set, cannot apply scaling and positioning");
            // If bunny is still not set, create a fallback
            await createFallbackBunny(scene);
            // Update local reference after fallback creation
        }

        // Adjust materials...
        if (bunny) {
            bunny.getChildMeshes(false).forEach(mesh => {
                mesh.receiveShadows = true;
                if (mesh.material instanceof BABYLON.StandardMaterial) {
                    mesh.material.emissiveColor = new BABYLON.Color3(0.05, 0.05, 0.08);
                    mesh.material.ambientColor = new BABYLON.Color3(0.4, 0.4, 0.5);
                    if (mesh.material.diffuseColor.r < 0.1 && mesh.material.diffuseColor.g < 0.1 && mesh.material.diffuseColor.b < 0.1) {
                        mesh.material.diffuseColor = new BABYLON.Color3(0.5, 0.5, 0.5);
                    }
                } else if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE && mesh.material) {
                    console.warn(`Material for bunny submesh ${mesh.name} not StandardMaterial.`);
                }
            });
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Bunny model processed.");
        }

        // Add shadows...
        if (bunny) {
            const shadowGenerator = scene.lights.find(l => l.getShadowGenerator())?.getShadowGenerator();
            if (shadowGenerator) {
                // If we have the result object from ImportMeshAsync
                if (typeof result !== 'undefined' && result && result.meshes) {
                    result.meshes.forEach(mesh => {
                        shadowGenerator.addShadowCaster(mesh);
                        mesh.receiveShadows = true; // Ensure again
                        mesh.getChildMeshes(true).forEach(submesh => { // Include children recursively
                            shadowGenerator.addShadowCaster(submesh);
                            submesh.receiveShadows = true;
                        });
                    });
                } else {
                    // If result is not available, just add the bunny mesh
                    shadowGenerator.addShadowCaster(bunny);
                    bunny.receiveShadows = true;
                    bunny.getChildMeshes(true).forEach(submesh => {
                        shadowGenerator.addShadowCaster(submesh);
                        submesh.receiveShadows = true;
                    });
                }
                if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Shadows added to bunny model and children.");
            } else if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.warn("No ShadowGenerator found when creating bunny.");
            }
        }

    } catch (error) {
        console.error("Error loading bunny model:", error); // Keep critical error
        createFallbackBunny(scene); // Create fallback if loading fails
    }

    // Player Spotlight - Zwiększone oświetlenie
    const lightPositionOffset = new BABYLON.Vector3(0, 0.8, 0.5);
    const lightDirection = new BABYLON.Vector3(0, -0.3, -1);
    const spotLight = new BABYLON.SpotLight("bunnySpotLight", lightPositionOffset, lightDirection, Math.PI / 3, 20, scene);
    spotLight.diffuse = new BABYLON.Color3(1, 1, 0.9);
    spotLight.intensity = 3.5; // Zwiększona intensywność światła
    spotLight.range = 60; // Zwiększony zasięg światła
    spotLight.shadowEnabled = true;
    spotLight.parent = bunnyCollider;

    // Dodatkowe światło punktowe dla lepszego oświetlenia
    const pointLight = new BABYLON.PointLight("bunnyPointLight", new BABYLON.Vector3(0, 0.5, 0), scene);
    pointLight.diffuse = new BABYLON.Color3(1, 1, 1);
    pointLight.intensity = 1.5;
    pointLight.range = 30;
    pointLight.parent = bunnyCollider;


    // Shadow Generator for spotlight...
    let shadowGenerator = scene.lights.find(l => l.getShadowGenerator())?.getShadowGenerator();
    if (!shadowGenerator && spotLight.shadowEnabled) {
        try {
            shadowGenerator = new BABYLON.ShadowGenerator(1024, spotLight);
            shadowGenerator.useExponentialShadowMap = true;
            shadowGenerator.useBlurExponentialShadowMap = true;
            shadowGenerator.blurKernel = 32;
            shadowGenerator.shadowMinZ = 1;
            shadowGenerator.shadowMaxZ = spotLight.range * 1.5;
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Created ShadowGenerator for bunny spotlight.");
            // Re-add casters if bunny exists already
            if (bunny) {
                if (bunny.name === "loadedBunny") {
                    bunny.getChildMeshes(true).forEach(mesh => shadowGenerator.addShadowCaster(mesh));
                } else if (bunny.name.includes("FallbackBunny")) {
                    shadowGenerator.addShadowCaster(bunny);
                }
            }
        } catch (e) {
            console.error("Failed to create ShadowGenerator:", e);
            spotLight.shadowEnabled = false;
        }
    }
    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("createBunny setup complete.");
    // Po utworzeniu królika automatycznie skonfiguruj kamerę
    if (typeof configureCamera === 'function') {
        configureCamera();
        if (typeof resetCameraPosition === 'function') {
            resetCameraPosition();
        }
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            console.log("[PLAYER] Camera auto-attached to player after bunny creation.");
        }
    }
}


async function createFallbackBunny(scene) {
    // Simple sphere as fallback - reduced size to fit better in blood vessels
    const newBunny = BABYLON.MeshBuilder.CreateSphere("fallbackBunny", {diameter: 0.4}, scene);
    const fallbackMat = new BABYLON.StandardMaterial("fallbackMat", scene);
    fallbackMat.diffuseColor = new BABYLON.Color3(0.8, 0.8, 0.8);
    newBunny.material = fallbackMat;
    newBunny.parent = bunnyCollider; // Attach to collider
    newBunny.position = BABYLON.Vector3.Zero(); // Center in collider

    // Update local reference after setting global
    bunny = newBunny;
    console.warn("Created fallback bunny (sphere)."); // Keep warning
}


function configureCamera(sceneParam = null, forceRecreate = false) {
    // Ensure previous camera is disposed if reconfiguring
    const localScene = sceneParam || scene;

    console.log("[CAMERA] Configuring camera for scene:", localScene ? 'VALID' : 'NULL');
    console.log("[CAMERA] Camera mode:", cameraMode);
    console.log("[CAMERA] BunnyCollider exists:", !!bunnyCollider);
    console.log("[CAMERA] TunnelSections length:", tunnelSections ? tunnelSections.length : 0);

    // Znajdź aktualną sekcję tunelu, aby umieścić kamerę w środku naczynia
    let currentSection = null;
    if (bunnyCollider && typeof findSectionAtPosition === 'function') {
        currentSection = findSectionAtPosition(bunnyCollider.position.z);
    }

    // Upewnij się, że mamy informację o sekcji tunelu
    if (!currentSection && tunnelSections && tunnelSections.length > 0) {
        // Jeśli nie znaleziono sekcji, użyj pierwszej dostępnej
        currentSection = tunnelSections[0];
    }

    // Sprawdź, czy kamera już istnieje i czy nie wymuszamy jej ponownego utworzenia
    const cameraExists = camera && localScene.activeCamera === camera;

    // Tylko utwórz nową kamerę, jeśli nie istnieje lub wymuszamy jej ponowne utworzenie
    if (!cameraExists || forceRecreate) {
        // Usuń istniejącą kamerę, jeśli istnieje
        if (localScene.activeCamera) {
            console.log("Disposing existing camera:", localScene.activeCamera.name);
            localScene.activeCamera.dispose();
        }

        // Ustaw stałą pozycję kamery
        const initialCameraPosition = new BABYLON.Vector3(0, 0, 0);

        if (cameraMode === 'TPP') {
            // Create a new ArcRotateCamera for TPP mode
            camera = new BABYLON.ArcRotateCamera("tppCamera", Math.PI, Math.PI / 3, 3, initialCameraPosition, localScene);
            camera.minZ = 0.1;
            camera.maxZ = 1500;

            if (bunnyCollider && tunnelSections && tunnelSections.length > 0) {
                const tunnelStart = tunnelSections[0].centerPoint.clone();
                const playerPos = bunnyCollider.position.clone();
                const tunnelVec = playerPos.subtract(tunnelStart);
                const tunnelDir = tunnelVec.normalize();
                const cameraDistanceFromPlayer = 2.0; // Zmniejszona odległość dla lepszego widoku
                const cameraPos = playerPos.subtract(tunnelDir.scale(cameraDistanceFromPlayer));
                camera.position.copyFrom(cameraPos);
                // Kamera patrzy na gracza (nie w przód tunelu)
                camera.setTarget(playerPos);
            } else if (bunnyCollider) {
                const behindVector = new BABYLON.Vector3(0, 0, 1);
                camera.position.copyFrom(bunnyCollider.position);
                camera.position.addInPlace(behindVector.scale(Config.CAMERA_RADIUS || 0.8));
                camera.position.y += 1.5;
                camera.setTarget(bunnyCollider.position);
            }

            // Set as active camera
            localScene.activeCamera = camera;

            // Synchronizuj z globalną zmienną
            window.camera = camera;

            cameraReachedPlayer = true;
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[PLAYER] Camera configured & attached to player automatically.");
            }
        } else if (cameraMode === 'FPP') {
            // Ustaw kamerę w trybie FPP
            const fppPosition = bunnyCollider ? bunnyCollider.position.clone() : new BABYLON.Vector3(0, 0, 0);
            // Dodaj przesunięcie w dół, aby kamera była bardziej wycentrowana
            fppPosition.y -= 0.3;
            camera = new BABYLON.FreeCamera("firstPersonCamera", fppPosition, localScene);

            // Znajdź kierunek ruchu (zgodny z kierunkiem tunelu)
            let forwardDirection;
            if (currentSection) {
                // POPRAWKA: Gracz porusza się w kierunku -Z (od początku do końca sekcji)
                forwardDirection = currentSection.startPoint.subtract(currentSection.endPoint).normalize();
            } else {
                // Fallback - użyj domyślnego kierunku do przodu (wzdłuż osi -Z)
                forwardDirection = new BABYLON.Vector3(0, 0, -1);
            }

            // Ustaw rotację kamery, aby patrzyła w kierunku ruchu
            const forwardQuat = BABYLON.Quaternion.RotationAxis(BABYLON.Axis.Y, 0);
            camera.rotationQuaternion = forwardQuat;

            // Ustaw wektor "up" kamery
            camera.upVector = BABYLON.Axis.Y;

            // Ustaw kierunek patrzenia kamery - patrzymy przed siebie, a nie na gracza
            // Dodaj wektor kierunku do pozycji kamery, aby patrzeć przed siebie
            const lookTarget = camera.position.add(forwardDirection);
            camera.setTarget(lookTarget);

            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[CAMERA] FPP Forward direction:", forwardDirection.toString());
                console.log("[CAMERA] FPP Look target:", lookTarget.toString());
                console.log("[CAMERA] FPP Camera position:", camera.position.toString());
            }

            camera.fov = Config.CAMERA_FOV;
            camera.minZ = 0.1;
            camera.maxZ = 1500;

            // Ustaw kamerę jako aktywną
            localScene.activeCamera = camera;

            // Synchronizuj z globalną zmienną
            window.camera = camera;

            // Ustaw flagę cameraReachedPlayer na true
            cameraReachedPlayer = true;
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[PLAYER] Camera configured & attached to player automatically.");
            }
            console.log("[CAMERA] First-person camera configured.");
        }
    } else {
        // Kamera już istnieje, więc tylko aktualizujemy jej pozycję
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            console.log("[PLAYER] Using existing camera, updating position only.");
        }
    }

    // Final camera check
    if (camera) {
        console.log("[CAMERA] Camera successfully created:", camera.name);
        console.log("[CAMERA] Camera position:", camera.position.toString());
        console.log("[CAMERA] Scene active camera:", localScene.activeCamera ? localScene.activeCamera.name : 'NONE');
    } else {
        console.error("[CAMERA] FAILED to create camera!");
    }
}


// Update player position and handle tunnel boundaries / wall collisions
function updateBunnyPosition(deltaTime) {
    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !gameRunning || !cameraReachedPlayer) return;

    // --- Movement Speeds ---
    const effectiveLateralSpeed = Config.BUNNY_LATERAL_SPEED * 100 * deltaTime;
    const effectiveVerticalSpeed = Config.BUNNY_VERTICAL_SPEED * 100 * deltaTime;
    const forwardSpeed = Config.BASE_SPEED * currentSpeedMultiplier * 100 * deltaTime;

    // --- Find Current Tunnel Section and Orientation ---
    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) {
        console.error("Player outside tunnel bounds!");
        gameOver();
        return;
    }

    const nextSectionIndex = Math.max(0, currentSection.index - 1);
    const nextSection = tunnelSections[nextSectionIndex];
    const safeNextCenter = nextSection ? nextSection.centerPoint : currentSection.centerPoint;
    const safeNextStart = nextSection ? nextSection.startPoint : currentSection.startPoint;
    const safeNextEnd = nextSection ? nextSection.endPoint : currentSection.endPoint;

    const sectionProgress = Math.max(0, Math.min(1, BABYLON.Vector3.Distance(bunnyCollider.position, currentSection.startPoint) / currentSection.length));
    const interpolatedCenter = BABYLON.Vector3.Lerp(currentSection.centerPoint, safeNextCenter, sectionProgress);

    // Interpolated backward direction (looking into the tunnel)
    const currentBackDir = currentSection.startPoint.subtract(currentSection.endPoint).normalize();
    const nextBackDir = safeNextStart.subtract(safeNextEnd).normalize();

    let interpolatedBackwardDirection = BABYLON.Vector3.Lerp(currentBackDir, nextBackDir, sectionProgress);
    if (interpolatedBackwardDirection.lengthSquared() > 0.001) {
        interpolatedBackwardDirection.normalize();
    } else {
        interpolatedBackwardDirection.copyFrom(currentBackDir);
    }

    // Calculate local Up and Right vectors
    let upVector;
    const dotUp = BABYLON.Vector3.Dot(interpolatedBackwardDirection, BABYLON.Axis.Y);
    if (Math.abs(dotUp) < 0.99) {
        const rightTemp = BABYLON.Vector3.Cross(BABYLON.Axis.Y, interpolatedBackwardDirection).normalize();
        upVector = BABYLON.Vector3.Cross(interpolatedBackwardDirection, rightTemp).normalize();
    } else {
        const rightTemp = BABYLON.Vector3.Cross(interpolatedBackwardDirection, BABYLON.Axis.X).normalize();
        upVector = BABYLON.Vector3.Cross(interpolatedBackwardDirection, rightTemp).normalize();
    }
    if (upVector.lengthSquared() < 0.1) upVector = BABYLON.Axis.Y;

    const rightVector = BABYLON.Vector3.Cross(upVector, interpolatedBackwardDirection).normalize();
    if (rightVector.lengthSquared() < 0.1) {
        const forwardFallback = interpolatedBackwardDirection.lengthSquared() > 0.1 ? interpolatedBackwardDirection : BABYLON.Axis.Z.negate();
        rightVector.copyFrom(BABYLON.Vector3.Cross(BABYLON.Axis.Y, forwardFallback).normalize());
    }

    // --- Input Handling ---
    let lateralMove = 0;
    let verticalMove = 0;

    // Device Orientation OR Keyboard
    if (deviceOrientation.available && deviceOrientation.permissionGranted) {
        const tiltSensitivity = 0.08;
        const deadZone = 5;

        if (Math.abs(deviceOrientation.gamma) > deadZone) {
            lateralMove = -(deviceOrientation.gamma / 60);
        }
        if (Math.abs(deviceOrientation.beta) > deadZone) {
            verticalMove = -(deviceOrientation.beta / 60);
        }

        lateralMove = Math.max(-1, Math.min(1, lateralMove));
        verticalMove = Math.max(-1, Math.min(1, verticalMove));
        lateralMove *= tiltSensitivity * 15;
        verticalMove *= tiltSensitivity * 15;
    }

    // Keyboard Input
    if (keysPressed['ArrowLeft'] || keysPressed['a'] || keysPressed['KeyA'] || keysPressed['mobileLeft']) lateralMove -= 1;
    if (keysPressed['ArrowRight'] || keysPressed['d'] || keysPressed['KeyD'] || keysPressed['mobileRight']) lateralMove += 1;
    if (keysPressed['ArrowUp'] || keysPressed['w'] || keysPressed['KeyW'] || keysPressed['mobileUp']) verticalMove += 1;
    if (keysPressed['ArrowDown'] || keysPressed['s'] || keysPressed['KeyS'] || keysPressed['mobileDown']) verticalMove -= 1;

    const controlSensitivity = currentSection.narrowing ? 0.5 : 1.0;

    // --- NOWY SYSTEM: Sprawdź ruch PRZED jego wykonaniem ---
    const proposedLateralDisplacement = rightVector.scale(lateralMove * effectiveLateralSpeed * controlSensitivity);
    const proposedVerticalDisplacement = upVector.scale(verticalMove * effectiveVerticalSpeed * controlSensitivity);
    const proposedBackwardDisplacement = interpolatedBackwardDirection.scale(forwardSpeed);

    // Oblicz proponowaną nową pozycję
    const proposedPosition = bunnyCollider.position.clone()
        .addInPlace(proposedLateralDisplacement)
        .addInPlace(proposedVerticalDisplacement)
        .addInPlace(proposedBackwardDisplacement);

    // Sprawdź czy proponowana pozycja jest w tunelu
    const offsetFromCenter = proposedPosition.subtract(interpolatedCenter);
    const distanceAlongAxis = BABYLON.Vector3.Dot(offsetFromCenter, interpolatedBackwardDirection);
    const radialOffsetVector = offsetFromCenter.subtract(interpolatedBackwardDirection.scale(distanceAlongAxis));
    const distanceFromAxis = radialOffsetVector.length();
    const tunnelRadius = currentSection.radius;

    // Granica bezpieczeństwa - 95% promienia tunelu
    const safeRadius = tunnelRadius * 0.95;

    if (distanceFromAxis <= safeRadius) {
        // Ruch jest bezpieczny - wykonaj go
        bunnyCollider.position.copyFrom(proposedPosition);
    } else {
        // Ruch wychodzi poza tunel - sprawdź który kierunek blokować

        // Testuj ruch do przodu (zawsze dozwolony)
        const forwardOnlyPosition = bunnyCollider.position.clone().addInPlace(proposedBackwardDisplacement);
        bunnyCollider.position.copyFrom(forwardOnlyPosition);

        // Testuj ruch boczny oddzielnie
        const lateralTestPosition = bunnyCollider.position.clone().addInPlace(proposedLateralDisplacement);
        const lateralOffsetFromCenter = lateralTestPosition.subtract(interpolatedCenter);
        const lateralDistanceAlongAxis = BABYLON.Vector3.Dot(lateralOffsetFromCenter, interpolatedBackwardDirection);
        const lateralRadialOffsetVector = lateralOffsetFromCenter.subtract(interpolatedBackwardDirection.scale(lateralDistanceAlongAxis));
        const lateralDistanceFromAxis = lateralRadialOffsetVector.length();

        if (lateralDistanceFromAxis <= safeRadius) {
            // Ruch boczny jest OK
            bunnyCollider.position.addInPlace(proposedLateralDisplacement);
        }

        // Testuj ruch pionowy oddzielnie
        const verticalTestPosition = bunnyCollider.position.clone().addInPlace(proposedVerticalDisplacement);
        const verticalOffsetFromCenter = verticalTestPosition.subtract(interpolatedCenter);
        const verticalDistanceAlongAxis = BABYLON.Vector3.Dot(verticalOffsetFromCenter, interpolatedBackwardDirection);
        const verticalRadialOffsetVector = verticalOffsetFromCenter.subtract(interpolatedBackwardDirection.scale(verticalDistanceAlongAxis));
        const verticalDistanceFromAxis = verticalRadialOffsetVector.length();

        if (verticalDistanceFromAxis <= safeRadius) {
            // Ruch pionowy jest OK
            bunnyCollider.position.addInPlace(proposedVerticalDisplacement);
        }
    }

    // Pokaż ostrzeżenie ścienne - z poprawnym obliczaniem odległości i hysterezą
    const currentOffsetFromCenter = bunnyCollider.position.subtract(interpolatedCenter);
    const currentDistanceAlongAxis = BABYLON.Vector3.Dot(currentOffsetFromCenter, interpolatedBackwardDirection);
    const currentRadialOffsetVector = currentOffsetFromCenter.subtract(interpolatedBackwardDirection.scale(currentDistanceAlongAxis));
    const currentDistanceFromAxis = currentRadialOffsetVector.length();
    const distanceToWall = Math.max(0, tunnelRadius - currentDistanceFromAxis);

    // Wywołaj funkcję ostrzeżenia tylko jeśli gracz jest relatywnie blisko ściany
    if (distanceToWall < tunnelRadius * 0.4) { // Tylko jeśli bliżej niż 40% promienia
        showTunnelWallWarning(distanceToWall, tunnelRadius);
    } else {
        // Ukryj ostrzeżenie jeśli gracz jest daleko od ściany
        const warningElement = document.getElementById('tunnelWallWarning');
        if (warningElement && warningElement._isActive) {
            warningElement._isActive = false;
            warningElement.style.opacity = '0';
            warningElement.style.borderWidth = '0px';
        }
    }

    // --- Update Rotation (zmieniono kierunek patrzenia) ---
    // Negujemy kierunek, aby gracz patrzył do przodu tunelu, a nie do tyłu
    const lookDirection = interpolatedBackwardDirection.negate();
    const targetQuaternion = BABYLON.Quaternion.FromLookDirectionLH(lookDirection, upVector);

    const rollFactor = Math.min(1.2, currentSpeedMultiplier);
    const rollAngle = -lateralMove * (Math.PI / 9.0) * rollFactor * controlSensitivity;
    const rollQuaternion = BABYLON.Quaternion.RotationAxis(lookDirection, rollAngle);

    const pitchAngle = verticalMove * (Math.PI / 12.0) * controlSensitivity;
    const pitchQuaternion = BABYLON.Quaternion.RotationAxis(rightVector, pitchAngle);

    const finalQuaternion = targetQuaternion.multiply(pitchQuaternion).multiply(rollQuaternion);

    const rotationLerpSpeed = 0.04;
    if (!bunnyCollider.rotationQuaternion) {
        bunnyCollider.rotationQuaternion = BABYLON.Quaternion.Identity();
    }

    bunnyCollider.rotationQuaternion = BABYLON.Quaternion.Slerp(
        bunnyCollider.rotationQuaternion,
        finalQuaternion,
        rotationLerpSpeed
    );

    // --- Check for Reaching Tunnel Start (Level Completion) ---
    const firstSection = tunnelSections[0];
    if (firstSection && currentSection.index <= 1) {
        const endPoint = firstSection.startPoint;
        const endDirection = firstSection.endPoint.subtract(firstSection.startPoint).normalize();
        const playerOffset = bunnyCollider.position.subtract(endPoint);
        const progressDot = BABYLON.Vector3.Dot(playerOffset, endDirection);
        const completionThreshold = -1.0;

        if (progressDot >= completionThreshold) {
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log(`Player reached end trigger!`);
            if (typeof completeLevel === 'function') {
                completeLevel();
            } else {
                console.error("FATAL: completeLevel function is not defined!");
                gameOver();
            }
            return;
        }
    }

    const endThresholdDistance = 1.0;
    if (firstSection && currentSection.index <= 0 && BABYLON.Vector3.Distance(bunnyCollider.position, firstSection.startPoint) < endThresholdDistance) {
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Player reached the end of the tunnel!");
        completeLevel();
    }
}


// Note: This duplicate shootLaser function has been removed.
// The implementation using object pooling (defined earlier in the file) is used instead.

// Funkcja resetująca pozycję kamery
function resetCameraPosition() {
    if (!camera || !bunnyCollider) return;

    // Handle differently based on camera mode
    if (cameraMode === 'FPP') {
        // For FPP mode, we need to update the camera direction to look forward

        // Znajdź aktualną sekcję tunelu
        let currentSection = null;
        if (typeof findSectionAtPosition === 'function') {
            currentSection = findSectionAtPosition(bunnyCollider.position.z);
        }

        // Aktualizuj pozycję kamery, aby była wycentrowana (z przesunięciem w dół)
        if (bunnyCollider) {
            const newPosition = bunnyCollider.position.clone();
            // Dodaj przesunięcie w dół, aby kamera była bardziej wycentrowana
            newPosition.y -= 0.3;
            camera.position.copyFrom(newPosition);
        }

        if (currentSection) {
            // Znajdź kierunek ruchu (zgodny z kierunkiem tunelu)
            // POPRAWKA: Gracz porusza się w kierunku -Z (od początku do końca sekcji)
            const forwardDirection = currentSection.startPoint.subtract(currentSection.endPoint).normalize();

            // Ustaw kierunek patrzenia kamery do przodu (patrzymy przed siebie, a nie na gracza)
            camera.setTarget(camera.position.add(forwardDirection));
        }

        // Zawsze ustawiamy flagę cameraReachedPlayer na true dla trybu FPP
        cameraReachedPlayer = true;
        return; // Nie wykonujemy reszty funkcji dla trybu FPP
    }

    // For TPP mode, update camera target to ensure proper targeting
    if (cameraMode === 'TPP') {
        if (bunnyCollider && tunnelSections && tunnelSections.length > 0) {
            // Punkt początkowy tunelu (przyjmujemy pierwszy punkt z tunnelSections)
            const tunnelStart = tunnelSections[0].centerPoint.clone();
            const playerPos = bunnyCollider.position.clone();
            const tunnelVec = playerPos.subtract(tunnelStart);
            const tunnelDir = tunnelVec.normalize();
            const cameraDistanceFromPlayer = 2.0; // Zmniejszona odległość dla lepszego widoku
            const cameraPos = playerPos.subtract(tunnelDir.scale(cameraDistanceFromPlayer));
            camera.position.copyFrom(cameraPos);
            // Kamera patrzy na gracza (nie w przód tunelu)
            camera.setTarget(playerPos);
        } else if (bunnyCollider) {
            // Fallback do starej metody
            const behindVector = new BABYLON.Vector3(0, 0, 1);
            camera.position.copyFrom(bunnyCollider.position);
            camera.position.addInPlace(behindVector.scale(Config.CAMERA_RADIUS || 0.8));
            camera.position.y += 1.5;
            camera.setTarget(bunnyCollider.position);
        }
        cameraReachedPlayer = true;
        return;
    }

    // Znajdź aktualną sekcję tunelu, aby umieścić kamerę w środku naczynia
    let currentSection = null;
    if (typeof findSectionAtPosition === 'function') {
        currentSection = findSectionAtPosition(bunnyCollider.position.z);
    }

    // Jeśli nie znaleziono sekcji, nie możemy kontynuować
    if (!currentSection || !currentSection.centerPoint) {
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.warn("Cannot reset camera position: no current section found");
        return;
    }

    // Stopniowo przybliżaj kamerę do królika
    const targetDistance = Config.CAMERA_RADIUS || 0.8; // Docelowa odległość kamery od królika
    const currentDistance = BABYLON.Vector3.Distance(camera.position, bunnyCollider.position);

    // Oblicz nową odległość z płynnym przejściem
    let newDistance = currentDistance;
    if (currentDistance > targetDistance + 0.1) { // Dodaj małą tolerancję
        // Płynnie zmniejszaj odległość
        newDistance = Math.max(targetDistance, currentDistance - 0.2);
    }

    // Ustaw nową pozycję kamery - zawsze wewnątrz naczynia
    // Użyj środka sekcji tunelu dla pozycji kamery
    const idealPosition = new BABYLON.Vector3(
        currentSection.centerPoint.x,
        currentSection.centerPoint.y,
        bunnyCollider.position.z + newDistance
    );

    // Ogranicz pozycję kamery, aby nie wychodziła poza granice tunelu
    if (currentSection.radius) {
        // Maksymalny dozwolony promień dla kamery (80% promienia tunelu dla bezpieczeństwa)
        const maxCameraRadius = currentSection.radius * 0.8;

        // Oblicz wektor od środka tunelu do idealnej pozycji kamery (tylko w płaszczyźnie XY)
        const offsetVector = new BABYLON.Vector3(
            idealPosition.x - currentSection.centerPoint.x,
            idealPosition.y - currentSection.centerPoint.y,
            0
        );

        // Oblicz długość tego wektora (odległość od środka tunelu w płaszczyźnie XY)
        const distanceFromCenter = offsetVector.length();

        // Jeśli kamera wychodzi poza granice tunelu, przesuń ją do środka
        if (distanceFromCenter > maxCameraRadius) {
            // Normalizuj wektor offsetu
            if (distanceFromCenter > 0.001) {
                offsetVector.normalize();
            } else {
                // Jeśli wektor jest zbyt mały, użyj domyślnego kierunku
                offsetVector.x = 0;
                offsetVector.y = 1;
            }

            // Ustaw pozycję kamery na granicy bezpiecznego promienia
            idealPosition.x = currentSection.centerPoint.x + offsetVector.x * maxCameraRadius;
            idealPosition.y = currentSection.centerPoint.y + offsetVector.y * maxCameraRadius;
        }
    }

    // Upewnij się, że kamera nie wychodzi poza granice tunelu w kierunku Z
    // Znajdź sekcję tunelu dla pozycji Z kamery
    const cameraSection = findSectionAtPosition(idealPosition.z);
    if (cameraSection && cameraSection.centerPoint) {
        // Użyj środka sekcji dla pozycji X i Y kamery
        idealPosition.x = cameraSection.centerPoint.x;
        idealPosition.y = cameraSection.centerPoint.y;
    }

    camera.position = idealPosition.clone();

    // Calculate distance to ensure it's within threshold
    const distance = BABYLON.Vector3.Distance(camera.position, bunnyCollider.position);
    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log(`Camera reset: distance to player = ${distance.toFixed(2)}`);

    // Ensure cameraReachedPlayer is true if distance is within threshold
    const threshold = Config.CAMERA_RADIUS * 1.2 || 1.0;
    if (distance <= threshold) {
        cameraReachedPlayer = true;
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Camera has reached player after reset. Player controls enabled.");
    } else {
        // Force camera to reach player even if distance is too large
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) console.log("Camera distance exceeds threshold, but forcing cameraReachedPlayer = true");
        cameraReachedPlayer = true;
    }
}

// The createFallbackBunny function is already defined above (line 240)
// Inicjalizacja
function initPlayerSystems() {
    initLaserPool();
}
// Only log if DEBUG_MODE is explicitly true
if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
    console.log("player.js: Player functions loaded (DEBUG MODE).");
}

/**
 * Provides a quick overview of the entire tunnel by moving the camera through it
 * and then returning to the original position
 */
function showTunnelOverview() {
    if (!camera || !scene || !tunnelSections || tunnelSections.length === 0) {
        console.warn("Cannot show tunnel overview: missing camera, scene, or tunnel sections");
        return;
    }

    // Store original camera position and target for later restoration
    const originalPosition = camera.position.clone();
    const originalTarget = camera.target ? camera.target.clone() : null;

    // Disable player controls during overview
    const wasGameRunning = gameRunning;
    const wasCameraReached = cameraReachedPlayer;
    gameRunning = false;
    cameraReachedPlayer = false;

    // Show message to user
    const messageDiv = document.createElement('div');
    messageDiv.id = 'overviewMessage';
    messageDiv.style.position = 'absolute';
    messageDiv.style.top = '20%';
    messageDiv.style.left = '50%';
    messageDiv.style.transform = 'translate(-50%, -50%)';
    messageDiv.style.color = 'white';
    messageDiv.style.fontSize = '24px';
    messageDiv.style.fontWeight = 'bold';
    messageDiv.style.textShadow = '2px 2px 4px rgba(0, 0, 0, 0.7)';
    messageDiv.style.zIndex = '1000';
    messageDiv.style.pointerEvents = 'none';
    messageDiv.textContent = 'Przegląd tunelu...';
    document.body.appendChild(messageDiv);

    // Calculate overview path
    const startSection = tunnelSections[tunnelSections.length - 1]; // Last section (start of tunnel)
    const endSection = tunnelSections[0]; // First section (end of tunnel)

    // Duration of overview in milliseconds
    const overviewDuration = 5000; // 5 seconds
    const startTime = performance.now();

    // Animation function
    function animateOverview() {
        const currentTime = performance.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(1, elapsed / overviewDuration);

        if (progress < 1) {
            // Move camera along the tunnel
            // Find the current section based on progress
            const sectionIndex = Math.floor((1 - progress) * (tunnelSections.length - 1));
            const currentSection = tunnelSections[sectionIndex];

            if (currentSection && currentSection.centerPoint) {
                // Position camera at the center of the current section
                camera.position.x = currentSection.centerPoint.x;
                camera.position.y = currentSection.centerPoint.y;
                camera.position.z = currentSection.centerPoint.z;

                // Look ahead in the tunnel
                const lookAheadIndex = Math.max(0, sectionIndex - 10); // Look 10 sections ahead
                const lookAheadSection = tunnelSections[lookAheadIndex];

                if (lookAheadSection && lookAheadSection.centerPoint) {
                    camera.setTarget(lookAheadSection.centerPoint);
                }
            }

            // Continue animation
            requestAnimationFrame(animateOverview);
        } else {
            // Animation complete, restore original camera position
            camera.position.copyFrom(originalPosition);
            if (originalTarget) {
                camera.setTarget(originalTarget);
            } else if (bunnyCollider) {
                camera.setTarget(bunnyCollider.position);
            }

            // Restore game state
            gameRunning = wasGameRunning;
            cameraReachedPlayer = wasCameraReached;

            // Remove message
            const messageDiv = document.getElementById('overviewMessage');
            if (messageDiv) {
                messageDiv.remove();
            }
        }
    }
    // Start animation
    animateOverview();
}

// Obsługa przełączania trybu kamery klawiszem V
// Zmieniona kolejność kamer: najpierw FPP, potem TPP
const cameraModes = ['FPP', 'TPP'];
let currentCameraIndex = cameraModes.indexOf(cameraMode);
if (currentCameraIndex === -1) currentCameraIndex = 0;

// Flaga do śledzenia trwającej zmiany kamery
// Używamy window.cameraTransitionInProgress, aby była dostępna globalnie
window.cameraTransitionInProgress = false;
let cameraTransitionStartTime = 0;
const CAMERA_TRANSITION_DURATION = 500; // ms

document.addEventListener('keydown', function(e) {
    if (e.code === 'KeyV' && !window.cameraTransitionInProgress && gameRunning) {
        // Zapamiętaj poprzedni tryb kamery
        const previousCameraMode = cameraMode;

        // Zmień tryb kamery
        currentCameraIndex = (currentCameraIndex + 1) % cameraModes.length;
        cameraMode = cameraModes[currentCameraIndex];

        // Rozpocznij płynne przejście tylko jeśli kamera i gracz istnieją
        if (camera && bunnyCollider) {
            window.cameraTransitionInProgress = true;
            cameraTransitionStartTime = performance.now();

            // Rozpocznij animację przejścia
            startCameraTransition(previousCameraMode, cameraMode);

            // Dodaj timeout jako zabezpieczenie
            setTimeout(() => {
                if (window.cameraTransitionInProgress) {
                    window.cameraTransitionInProgress = false;
                    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                        console.log("[PLAYER] Camera transition timeout - forcing completion");
                    }
                }
            }, CAMERA_TRANSITION_DURATION + 100);

            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[PLAYER] Camera mode switching to " + cameraMode + " with smooth transition.");
            }
        } else {
            // Jeśli nie ma kamery lub gracza, po prostu zmień tryb bez animacji
            window.cameraTransitionInProgress = false;
            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[PLAYER] Camera mode changed to " + cameraMode + " (no animation - missing camera/player).");
            }
        }
    }
});

/**
 * Rozpoczyna płynne przejście między trybami kamery z ograniczeniem do tunelu
 * @param {string} fromMode - Poprzedni tryb kamery
 * @param {string} toMode - Docelowy tryb kamery
 */
function startCameraTransition(fromMode, toMode) {
    if (!camera || !bunnyCollider) {
        window.cameraTransitionInProgress = false;
        return;
    }

    // Znajdź aktualną sekcję tunelu
    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) {
        window.cameraTransitionInProgress = false;
        return;
    }

    // Funkcja ograniczająca pozycję kamery do tunelu
    const constrainCameraToTunnel = (position, sectionAtZ) => {
        if (!sectionAtZ || !sectionAtZ.centerPoint) return position;

        // Oblicz odległość od środka tunelu w płaszczyźnie XY
        const dx = position.x - sectionAtZ.centerPoint.x;
        const dy = position.y - sectionAtZ.centerPoint.y;
        const distanceFromCenter = Math.sqrt(dx*dx + dy*dy);

        // Maksymalna dozwolona odległość (80% promienia tunelu)
        const maxDistance = sectionAtZ.radius * 0.8;

        if (distanceFromCenter > maxDistance) {
            // Skaluj pozycję do granic tunelu
            const scale = maxDistance / distanceFromCenter;
            return new BABYLON.Vector3(
                sectionAtZ.centerPoint.x + dx * scale,
                sectionAtZ.centerPoint.y + dy * scale,
                position.z
            );
        }

        return position;
    };

    // Zapisz początkową pozycję i cel kamery
    const startPosition = camera.position.clone();
    const startTarget = camera.getTarget ? camera.getTarget().clone() : bunnyCollider.position.clone();

    // Oblicz docelową pozycję i cel kamery
    let targetPosition, targetLookAt;

    if (toMode === 'FPP') {
        // Pozycja dla kamery FPP - przy graczu
        targetPosition = bunnyCollider.position.clone();
        targetPosition.y += 0.6; // Lekkie przesunięcie w górę

        // Kierunek patrzenia - do przodu tunelu
        // POPRAWKA: Gracz porusza się w kierunku -Z (od początku do końca sekcji)
        const forwardDirection = currentSection.startPoint.subtract(currentSection.endPoint).normalize();
        targetLookAt = targetPosition.add(forwardDirection);

    } else if (toMode === 'TPP') {
        // Pozycja dla kamery TPP - za graczem
        // Zmieniono z endPoint.subtract(startPoint) na startPoint.subtract(endPoint)
        // aby kierunek był zgodny z kierunkiem ruchu gracza do przodu
        const tunnelDirection = currentSection.startPoint.subtract(currentSection.endPoint).normalize();

        // Podstawowa pozycja za graczem
        let offsetDistance = Math.min(2.0, currentSection.radius * 0.6); // Dostosuj do rozmiaru tunelu
        targetPosition = bunnyCollider.position.subtract(tunnelDirection.scale(offsetDistance));
        targetPosition.y += Math.min(1.0, currentSection.radius * 0.3); // Lekko w górę

        // Sprawdź sekcję docelowej pozycji kamery
        const cameraSectionAtZ = findSectionAtPosition(targetPosition.z);

        // Ogranicz pozycję kamery do tunelu
        targetPosition = constrainCameraToTunnel(targetPosition, cameraSectionAtZ || currentSection);

        // Kamera patrzy na gracza
        targetLookAt = bunnyCollider.position.clone();
    }

    // Upewnij się, że docelowa pozycja jest w tunelu
    const targetSection = findSectionAtPosition(targetPosition.z);
    targetPosition = constrainCameraToTunnel(targetPosition, targetSection || currentSection);

    // Funkcja animacji przejścia
    function animateCameraTransition() {
        const now = performance.now();
        const elapsed = now - cameraTransitionStartTime;
        const progress = Math.min(1, elapsed / CAMERA_TRANSITION_DURATION);

        if (progress < 1) {
            // Interpolacja pozycji kamery
            const newPosition = BABYLON.Vector3.Lerp(startPosition, targetPosition, progress);

            // Ogranicz interpolowaną pozycję do tunelu
            const interpolatedSection = findSectionAtPosition(newPosition.z);
            const constrainedPosition = constrainCameraToTunnel(newPosition, interpolatedSection || currentSection);

            camera.position.copyFrom(constrainedPosition);

            // Interpolacja celu kamery
            const newTarget = BABYLON.Vector3.Lerp(startTarget, targetLookAt, progress);
            camera.setTarget(newTarget);

            // Kontynuuj animację
            requestAnimationFrame(animateCameraTransition);
        } else {
            // Zakończ przejście - upewnij się, że końcowa pozycja jest w tunelu
            const finalSection = findSectionAtPosition(targetPosition.z);
            const finalConstrainedPosition = constrainCameraToTunnel(targetPosition, finalSection || currentSection);

            camera.position.copyFrom(finalConstrainedPosition);
            camera.setTarget(targetLookAt);
            window.cameraTransitionInProgress = false;

            if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
                console.log("[PLAYER] Camera transition complete. Mode: " + toMode + ", Position constrained to tunnel");
            }
        }
    }

    // Rozpocznij animację
    animateCameraTransition();
}

/**
 * Shows a warning when the rabbit is approaching the tunnel wall
 * Improved version with:
 * - Hysteresis (different thresholds for showing/hiding)
 * - Rate limiting (max update frequency)
 * - Smooth transitions (lerp between values)
 * - Longer animation duration
 *
 * @param {number} distanceToWall - The distance from the rabbit to the tunnel wall
 * @param {number} tunnelRadius - The radius of the current tunnel section
 */
function showTunnelWallWarning(distanceToWall, tunnelRadius) {
    // Get or create the warning element
    let warningElement = document.getElementById('tunnelWallWarning');

    if (!warningElement) {
        // Create warning element if it doesn't exist
        warningElement = document.createElement('div');
        warningElement.id = 'tunnelWallWarning';
        warningElement.style.position = 'absolute';
        warningElement.style.top = '0';
        warningElement.style.left = '0';
        warningElement.style.width = '100%';
        warningElement.style.height = '100%';
        warningElement.style.border = '0px solid red';
        warningElement.style.boxSizing = 'border-box';
        warningElement.style.pointerEvents = 'none'; // Don't interfere with clicks
        warningElement.style.zIndex = '1000';
        warningElement.style.opacity = '0';
        warningElement.style.transition = 'opacity 0.3s, border-width 0.3s'; // Increased from 0.2s to 0.3s

        // Add properties for state tracking
        warningElement._isActive = false;
        warningElement._lastUpdateTime = 0;
        warningElement._currentIntensity = 0;

        document.body.appendChild(warningElement);
    }

    // Hysteresis thresholds - different values for activation and deactivation
    const activationThreshold = 0.25 * tunnelRadius;   // Show warning at 25% of radius
    const deactivationThreshold = 0.35 * tunnelRadius; // Hide warning at 35% of radius

    // Rate limiting - only update every 100ms
    const now = Date.now();
    if (now - (warningElement._lastUpdateTime || 0) < 100) {
        return; // Skip update if less than 100ms since last update
    }
    warningElement._lastUpdateTime = now;

    // Determine if warning should be active (with hysteresis)
    let shouldBeActive;
    if (warningElement._isActive) {
        // If already active, only deactivate when distance exceeds deactivation threshold
        shouldBeActive = distanceToWall < deactivationThreshold;
    } else {
        // If not active, only activate when distance is less than activation threshold
        shouldBeActive = distanceToWall < activationThreshold;
    }

    // Update active state
    warningElement._isActive = shouldBeActive;

    if (shouldBeActive) {
        // Calculate target intensity based on how close to the wall
        // (0 = at deactivation threshold, 1 = at wall)
        const targetIntensity = Math.min(1, 1 - (distanceToWall / activationThreshold));

        // Smooth transition using lerp (linear interpolation)
        warningElement._currentIntensity = warningElement._currentIntensity * 0.7 + targetIntensity * 0.3;

        // Update warning display
        warningElement.style.opacity = (warningElement._currentIntensity * 0.7).toString(); // Max opacity 0.7
        warningElement.style.borderWidth = (warningElement._currentIntensity * 20) + 'px'; // Max border width 20px
    } else {
        // Fade out smoothly
        warningElement._currentIntensity *= 0.7; // Decay factor

        if (warningElement._currentIntensity < 0.01) {
            // Hide completely when intensity is very low
            warningElement.style.opacity = '0';
            warningElement.style.borderWidth = '0px';
            warningElement._currentIntensity = 0;
        } else {
            // Fade out gradually
            warningElement.style.opacity = (warningElement._currentIntensity * 0.7).toString();
            warningElement.style.borderWidth = (warningElement._currentIntensity * 20) + 'px';
        }
    }
}

/**
 * Constrains the rabbit's position to stay inside the tunnel
 * @param {BABYLON.Mesh} rabbitMesh - The rabbit mesh to constrain
 * @param {BABYLON.Vector3} tunnelCenter - The center point of the current tunnel section
 * @param {number} tunnelRadius - The radius of the current tunnel section
 */
function constrainRabbitInsideTunnel(rabbitMesh, tunnelCenter, tunnelRadius) {
    // Calculate distance from tunnel center (in X/Y plane)
    const dx = rabbitMesh.position.x - tunnelCenter.x;
    const dy = rabbitMesh.position.y - tunnelCenter.y;
    const distance = Math.sqrt(dx*dx + dy*dy);

    // If rabbit is outside tunnel radius, move it back to the edge
    if (distance > tunnelRadius) {
        const scale = tunnelRadius / distance;
        rabbitMesh.position.x = tunnelCenter.x + dx * scale;
        rabbitMesh.position.y = tunnelCenter.y + dy * scale;

        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            console.log("Rabbit constrained to tunnel boundary");
        }
    }

    // Return the distance to wall for warning system
    return tunnelRadius - distance;
}

// Make functions available globally for non-module scripts
window.createBunny = createBunny;
window.resetCameraPosition = resetCameraPosition;
window.showTunnelOverview = showTunnelOverview;
window.constrainRabbitInsideTunnel = constrainRabbitInsideTunnel;
window.showTunnelWallWarning = showTunnelWallWarning;
window.startCameraTransition = startCameraTransition;
// cameraTransitionInProgress is already a window property

// --- END OF FILE player.js ---
