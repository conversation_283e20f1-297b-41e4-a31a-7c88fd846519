// Using DEBUG_MODE from globals.js

let multiplayerInitialized = false;

let qualityLevel = 'high'; // 'low', 'medium', 'high'

// Inicjalizacja gry po załadowaniu strony
window.addEventListener('DOMContentLoaded', function() {
    // ZMIANA: Zamiast od razu initGame(), pokaż menu startowe
    initGameEngine();
});

async function initGameEngine() {
    console.log("=== INITIALIZING GAME ENGINE ===");

    try {
        // 1. Sprawdź canvas
        const canvasElement = document.getElementById('gameCanvas');
        if (!canvasElement) {
            throw new Error("Game canvas not found - check HTML");
        }

        if (canvasElement) canvasElement.focus();

        // 2. Inicjalizuj Babylon.js z error handling
        if (!window.engine) {
            try {
                window.engine = new BABYLON.Engine(canvasElement, true, {
                    preserveDrawingBuffer: true,
                    stencil: true
                });
                console.log("✅ Babylon.js engine created");

                window.scene = new BABYLON.Scene(window.engine);

                // Basic lighting
                const light = new BABYLON.HemisphericLight("light", new BABYLON.Vector3(0, 1, 0), window.scene);

                // Default camera
                const camera = new BABYLON.FreeCamera("defaultCamera", new BABYLON.Vector3(0, 5, -10), window.scene);
                camera.setTarget(BABYLON.Vector3.Zero());

                console.log("✅ Babylon.js scene created");

                // Start the render loop
                window.engine.runRenderLoop(() => {
                    if (window.scene && window.scene.activeCamera) {
                        window.scene.render();
                    }
                });

                // Handle window resize
                window.addEventListener("resize", () => {
                    if (window.engine) {
                        window.engine.resize();
                    }
                });
            } catch (e) {
                console.error("❌ Error initializing Babylon.js:", e);
                throw e;
            }
        }

        // 3. Inicjalizuj game state
        if (!window.gameState) {
            window.gameState = {
                running: false,
                paused: false
            };
        }

        // 4. Czekaj na DOM przed inicjalizacją UI
        await waitForDOM();

        // 5. Inicjalizuj UI
        console.log("=== INITIALIZING UI SYSTEM ===");
        await initializeUI();

        // 6. Inicjalizuj game optimizer
        await initializeGameOptimizer();

        // 7. Pokaż start screen
        showStartScreen();

        console.log("=== GAME ENGINE INITIALIZATION COMPLETE ===");

    } catch (error) {
        console.error("❌ Game engine initialization failed:", error);
        showErrorScreen(error.message);
    }
}

// Helper functions for safe initialization
async function waitForDOM() {
    if (document.readyState === 'loading') {
        return new Promise(resolve => {
            document.addEventListener('DOMContentLoaded', resolve);
        });
    }
}

async function initializeUI() {
    try {
        if (typeof getUIElementReferences === 'function') {
            getUIElementReferences();
        }

        if (typeof setupUIEventListeners === 'function') {
            setupUIEventListeners();
        }

        console.log("✅ UI initialized");
    } catch (error) {
        console.error("❌ UI initialization failed:", error);
        throw error;
    }
}

async function initializeGameOptimizer() {
    try {
        // Poczekaj chwilę na załadowanie wszystkich skryptów i sceny
        await new Promise(resolve => setTimeout(resolve, 500));

        if (window.gameOptimizer && typeof window.gameOptimizer.initialize === 'function') {
            await window.gameOptimizer.initialize();
        } else if (typeof initialize === 'function') {
            const result = initialize();
            if (result) {
                console.log("✅ Game optimizer initialized");
            } else {
                console.log("⚠️ Game optimizer initialization pending (scene not ready)");
            }
        } else {
            console.log("⚠️ Game optimizer not available");
        }
    } catch (error) {
        console.warn("⚠️ Game optimizer initialization failed:", error);
        // Nie przerywaj inicjalizacji - optimizer nie jest krytyczny
    }
}

function showStartScreen() {
    setTimeout(() => {
        // Ukryj loading screen
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
            console.log("Loading screen hidden");
        }

        // Pokaż start screen
        const startScreen = safeGetElement('startScreen');
        if (startScreen) {
            startScreen.style.display = 'flex';
            console.log("Start screen shown");
        }

        // Inicjalizuj demo scene jako tło
        if (typeof window.initDemoScene === 'function') {
            try {
                window.initDemoScene(window.scene);
            } catch (error) {
                console.warn("Demo scene initialization failed:", error);
            }
        }
    }, 1000);
}

// Funkcja do bezpiecznego pobierania elementów DOM
function safeGetElement(elementId, createFallback = true) {
    let element = document.getElementById(elementId);

    if (!element && createFallback) {
        console.warn(`Creating fallback element for missing: ${elementId}`);
        element = document.createElement('div');
        element.id = elementId;
        element.style.display = 'none';
        document.body.appendChild(element);
    }

    return element;
}

// ============= 7. DODAJ DO KOŃCA main.js =============
// Upewnij się, że te funkcje są dostępne globalnie:

window.pauseGame = pauseGame;
window.returnToMainMenu = returnToMainMenu;
window.toggleCameraMode = toggleCameraMode;
window.initializeInputHandling = initializeInputHandling;
window.updatePlayerMovement = updatePlayerMovement;

console.log("=== GAME SYSTEM FULLY LOADED ===");

/**
 * Wyświetla ekran błędu w przypadku krytycznego problemu z inicjalizacją
 * @param {string} errorMessage - Komunikat błędu do wyświetlenia
 */
function showErrorScreen(errorMessage) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,0,0,0.8);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;

    errorDiv.innerHTML = `
        <h1>Błąd Inicjalizacji Gry</h1>
        <p>${errorMessage}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; font-size: 16px;">
            Odśwież stronę
        </button>
    `;

    document.body.appendChild(errorDiv);
}
