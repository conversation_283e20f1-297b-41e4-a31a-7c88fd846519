/**
 * BloodCellEmitterSystem.js
 * 
 * A system that creates blood cell emitters at each tunnel section,
 * automatically managing emitters based on player position.
 * 
 * Features:
 * - Creates emitters at each tunnel section
 * - Automatically manages 10 sections before/after the player
 * - Spawns 2-4 blood cells per emitter every 2 seconds
 * - Optimizes performance by only activating emitters near the player
 */

class BloodCellEmitterSystem {
    constructor(scene) {
        this.scene = scene;
        this.emitters = new Map(); // Map of section index → emitter
        this.activeEmitters = new Set(); // Set of active emitter indices
        this.cellsPerEmitter = { min: 2, max: 4 }; // 2-4 cells per spawn
        this.emitterRange = 10; // 10 sections before/after player
        this.emitterSpawnInterval = 2000; // 2 seconds between spawns
        this.lastSpawnTime = 0;
        this.bloodCells = []; // All active blood cells
        this.initialized = false;
        this.debugMode = typeof DEBUG_MODE !== 'undefined' ? DEBUG_MODE : false;
    }

    /**
     * Initializes the blood cell emitter system
     * @param {boolean} retry - Whether to retry initialization if dependencies aren't available
     * @returns {Promise<boolean>} - Promise resolving to true if initialization was successful
     */
    async initialize(retry = true) {
        if (this.initialized) return true;

        // Check dependencies
        const dependencies = this.checkDependencies();

        if (!dependencies.scene) {
            console.error("Cannot initialize BloodCellEmitterSystem: No scene provided");
            return false;
        }

        if (!dependencies.tunnelSections) {
            if (this.debugMode) {
                console.warn("BloodCellEmitterSystem: Tunnel sections not available, will retry later");
            }

            if (retry) {
                // Schedule retry after a delay
                return new Promise(resolve => {
                    setTimeout(() => {
                        this.initialize(retry).then(resolve);
                    }, 1000);
                });
            }

            return false;
        }

        this.initialized = true;
        this.lastSpawnTime = performance.now();

        if (this.debugMode) {
            console.log("BloodCellEmitterSystem initialized successfully");
        }

        return true;
    }

    /**
     * Checks if all dependencies are available
     * @returns {Object} - Object with boolean flags for each dependency
     */
    checkDependencies() {
        return {
            scene: !!this.scene,
            tunnelSections: Array.isArray(window.tunnelSections) && window.tunnelSections.length > 0,
            player: !!window.bunnyCollider
        };
    }

    /**
     * Updates the active emitters based on player position
     * @param {BABYLON.Vector3} playerPosition - The current player position
     */
    updateEmitters(playerPosition) {
        if (!this.initialized) this.initialize();
        if (!this.initialized) return;

        if (!playerPosition || !Array.isArray(tunnelSections) || tunnelSections.length === 0) {
            return;
        }

        // Find the current player section
        let currentSectionIndex = -1;
        let minDistance = Infinity;

        for (let i = 0; i < tunnelSections.length; i++) {
            const section = tunnelSections[i];
            const distance = BABYLON.Vector3.Distance(playerPosition, section.centerPoint);

            if (distance < minDistance) {
                minDistance = distance;
                currentSectionIndex = i;
            }
        }

        if (currentSectionIndex === -1) return;

        // Calculate range of sections to activate
        const startIndex = Math.max(0, currentSectionIndex - this.emitterRange);
        const endIndex = Math.min(tunnelSections.length - 1, currentSectionIndex + this.emitterRange);

        // Clear previous active emitters
        this.activeEmitters.clear();

        // Activate emitters within range
        for (let i = startIndex; i <= endIndex; i++) {
            this.activeEmitters.add(i);

            // Create emitter if it doesn't exist
            if (!this.emitters.has(i)) {
                this.createEmitterForSection(i);
            }
        }

        // Clean up emitters that are far from player
        for (const [index, emitter] of this.emitters.entries()) {
            if (!this.activeEmitters.has(index)) {
                // Keep the emitter object but mark it as inactive
                emitter.active = false;
            }
        }

        if (this.debugMode) {
            console.log(`Active emitters: ${this.activeEmitters.size}, Total emitters: ${this.emitters.size}`);
        }
    }

    /**
     * Creates an emitter for a specific tunnel section
     * @param {number} sectionIndex - The index of the tunnel section
     */
    createEmitterForSection(sectionIndex) {
        if (!Array.isArray(tunnelSections) || !tunnelSections[sectionIndex]) {
            return;
        }

        const section = tunnelSections[sectionIndex];

        // Create emitter object
        const emitter = {
            position: section.centerPoint.clone(),
            radius: section.radius,
            active: true,
            lastSpawnTime: 0
        };

        this.emitters.set(sectionIndex, emitter);

        if (this.debugMode) {
            console.log(`Created emitter for section ${sectionIndex} at position ${emitter.position}`);
        }
    }

    /**
     * Spawns blood cells from active emitters
     */
    spawnBloodCellsFromEmitters() {
        if (!this.initialized) return;

        const currentTime = performance.now();
        if (currentTime - this.lastSpawnTime < this.emitterSpawnInterval) {
            return;
        }

        this.lastSpawnTime = currentTime;

        // Spawn cells from active emitters
        for (const sectionIndex of this.activeEmitters) {
            const emitter = this.emitters.get(sectionIndex);
            if (!emitter || !emitter.active) continue;

            // Determine number of cells to spawn (2-4)
            const cellCount = Math.floor(Math.random() * 
                (this.cellsPerEmitter.max - this.cellsPerEmitter.min + 1)) + 
                this.cellsPerEmitter.min;

            for (let i = 0; i < cellCount; i++) {
                this.spawnBloodCell(emitter);
            }
        }
    }

    /**
     * Spawns a single blood cell from an emitter
     * @param {Object} emitter - The emitter object
     */
    spawnBloodCell(emitter) {
        if (!emitter || !emitter.position || !emitter.radius) return;

        // Calculate random position within the tunnel section
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * emitter.radius * 0.8; // 80% of radius to avoid walls

        const position = new BABYLON.Vector3(
            emitter.position.x + Math.cos(angle) * distance,
            emitter.position.y + Math.sin(angle) * distance,
            emitter.position.z
        );

        // Create blood cell mesh
        let bloodCellMesh;

        if (typeof createRedBloodCellMesh === 'function') {
            // Use existing function if available
            bloodCellMesh = createRedBloodCellMesh(position, emitter.radius * 0.05);
        } else {
            // Create simple sphere as fallback
            bloodCellMesh = BABYLON.MeshBuilder.CreateSphere("bloodCell", {
                diameter: emitter.radius * 0.1
            }, this.scene);

            // Apply material
            const material = new BABYLON.StandardMaterial("bloodCellMat", this.scene);
            material.diffuseColor = new BABYLON.Color3(0.8, 0, 0);
            material.emissiveColor = new BABYLON.Color3(0.3, 0, 0);
            bloodCellMesh.material = material;
        }

        if (!bloodCellMesh) return;

        bloodCellMesh.position = position;

        // Add random velocity
        const velocity = new BABYLON.Vector3(
            (Math.random() - 0.5) * 0.05,
            (Math.random() - 0.5) * 0.05,
            -0.1 - Math.random() * 0.1 // Moving in negative Z direction (forward in tunnel)
        );

        // Add to blood cells array
        this.bloodCells.push({
            mesh: bloodCellMesh,
            velocity: velocity,
            age: 0,
            maxAge: 30 + Math.random() * 20 // 30-50 seconds lifetime
        });
    }

    /**
     * Updates all blood cells
     * @param {number} deltaTime - Time since last update in seconds
     */
    updateBloodCells(deltaTime) {
        if (!this.initialized) return;

        // Update positions of all blood cells
        for (let i = this.bloodCells.length - 1; i >= 0; i--) {
            const cell = this.bloodCells[i];

            // Update age
            cell.age += deltaTime;

            // Remove old cells
            if (cell.age > cell.maxAge) {
                if (cell.mesh) {
                    cell.mesh.dispose();
                }
                this.bloodCells.splice(i, 1);
                continue;
            }

            // Update position
            if (cell.mesh && cell.velocity) {
                cell.mesh.position.addInPlace(cell.velocity.scale(deltaTime * 60));

                // Add wobble effect
                cell.mesh.position.y += Math.sin(performance.now() * 0.001 + i) * 0.001 * deltaTime * 60;

                // Add rotation
                cell.mesh.rotation.x += 0.01 * deltaTime * 60;
                cell.mesh.rotation.y += 0.02 * deltaTime * 60;
                cell.mesh.rotation.z += 0.005 * deltaTime * 60;
            }
        }
    }

    /**
     * Updates the entire blood cell emitter system
     * @param {number} deltaTime - Time since last update in seconds
     */
    async update(deltaTime) {
        if (!this.initialized) {
            // Try to initialize, but don't wait for it to complete
            this.initialize().catch(err => {
                if (this.debugMode) {
                    console.warn("Error initializing BloodEmitterSystem:", err);
                }
            });
            return; // Skip this update cycle
        }

        // Check dependencies again to be safe
        const dependencies = this.checkDependencies();
        if (!dependencies.player || !dependencies.tunnelSections) return;

        // Get player position
        const playerPosition = window.bunnyCollider ? window.bunnyCollider.position : null;
        if (!playerPosition) return;

        // Update emitters based on player position
        this.updateEmitters(playerPosition);

        // Spawn new blood cells
        this.spawnBloodCellsFromEmitters();

        // Update existing blood cells
        this.updateBloodCells(deltaTime);
    }

    /**
     * Cleans up all blood cells and emitters
     */
    cleanup() {
        // Dispose all blood cell meshes
        for (const cell of this.bloodCells) {
            if (cell.mesh) {
                cell.mesh.dispose();
            }
        }

        this.bloodCells = [];
        this.emitters.clear();
        this.activeEmitters.clear();

        if (this.debugMode) {
            console.log("BloodCellEmitterSystem cleaned up");
        }
    }

    /**
     * Gets statistics about the blood cell emitter system
     * @returns {Object} Statistics object
     */
    getStats() {
        return {
            activeEmitters: this.activeEmitters.size,
            totalEmitters: this.emitters.size,
            bloodCells: this.bloodCells.length
        };
    }
}

// Create global instance
let bloodEmitterSystem = null;

/**
 * Initializes the blood cell emitter system
 * @returns {Promise<boolean>} - Promise resolving to true if initialization was successful
 */
async function initializeBloodEmitterSystem() {
    if (bloodEmitterSystem) {
        bloodEmitterSystem.cleanup();
    }

    // Use window.scene to ensure we're using the global scene
    bloodEmitterSystem = new BloodCellEmitterSystem(window.scene);

    try {
        // Initialize with retry mechanism
        const success = await bloodEmitterSystem.initialize(true);

        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            if (success) {
                console.log("✅ Blood emitter system initialized successfully");
            } else {
                console.warn("⚠️ Blood emitter system initialization pending");
            }
        }

        return success;
    } catch (error) {
        console.error("❌ Blood emitter system initialization failed:", error);
        return false;
    }
}

/**
 * Updates the blood cell emitter system
 * @param {number} deltaTime - Time since last update in seconds
 */
async function updateBloodEmitterSystem(deltaTime) {
    if (!bloodEmitterSystem) {
        await initializeBloodEmitterSystem();
    }

    if (bloodEmitterSystem) {
        await bloodEmitterSystem.update(deltaTime);
    }
}

// Export to global scope
window.bloodEmitterSystem = bloodEmitterSystem;
window.initializeBloodEmitterSystem = initializeBloodEmitterSystem;
window.updateBloodEmitterSystem = updateBloodEmitterSystem;
